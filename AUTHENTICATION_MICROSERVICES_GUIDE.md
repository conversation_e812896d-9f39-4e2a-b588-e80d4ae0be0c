# Authentication & Microservices Integration Guide

## 🔐 Overview

This guide explains how authentication works across the NGNair microservices ecosystem using Ruby on Rails backend and Next.js frontend.

## 🏗️ Architecture

### Authentication Service
- **Domain:** `ng-auth-dev.dev1.ngnair.com`
- **Backend:** Ruby on Rails API
- **Frontend:** Next.js Authentication UI
- **Purpose:** Centralized authentication for all microservices

### Microservices Ecosystem
- **Support:** `ng-support-fe-dev.dev1.ngnair.com`
- **Accounts:** `ng-accounts-fe-dev.dev1.ngnair.com`
- **Other services:** `ng-*.ngnair.com` pattern

## 🔄 Authentication Flow

### 1. Initial Login Request

```
User → Microservice → Auth Service → Rails Backend
```

**Example:**
```
https://ng-support.ngnair.com/dashboard
↓ (not authenticated)
https://ng-auth-dev.dev1.ngnair.com/login?redirect=https://ng-support.ngnair.com/dashboard
```

### 2. Login API Call

**Endpoint:** `POST /api/v1/login?device_id`

**Headers:**
```
Content-Type: application/json
X-Device-ID: device-uuid-123
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

**Success Response:**
```json
{
  "device_id": "device-uuid-123",
  "success": "True"
}
```

### 3. Rails Backend Actions

When login is successful, Rails backend:

#### Sets HTTP-Only Cookies
```ruby
cookies.signed[:auth_token] = {
  value: jwt_token,
  domain: '.ngnair.com',
  httponly: true,
  secure: true,
  samesite: :lax,
  expires: 24.hours.from_now
}

cookies.signed[:device_id] = {
  value: device_id,
  domain: '.ngnair.com',
  httponly: true,
  secure: true,
  samesite: :lax
}
```

#### Generates JWT Token
```ruby
jwt_payload = {
  user_id: user.id,
  email: user.email,
  device_id: device_id,
  roles: user.roles,
  permissions: user.permissions,
  exp: 24.hours.from_now.to_i,
  iat: Time.current.to_i
}

jwt_token = JWT.encode(jwt_payload, Rails.application.secrets.secret_key_base)
```

### 4. Frontend Redirect

After successful authentication:

```javascript
// Check for redirect parameter
const searchParams = new URLSearchParams(window.location.search);
const redirectTo = searchParams.get('redirect');

if (redirectTo) {
  // Redirect back to original microservice
  window.location.href = redirectTo;
} else {
  // Fallback to accounts frontend
  window.location.href = 'https://ng-accounts-fe-dev.dev1.ngnair.com';
}
```

## 🍪 Cookie-Based Authentication

### Stored Cookies

| Cookie | Purpose | Domain | Security |
|--------|---------|---------|----------|
| `auth_token` | JWT with user info | `.ngnair.com` | HTTP-Only, Secure |
| `device_id` | Device identifier | `.ngnair.com` | HTTP-Only, Secure |
| `refresh_token` | Token renewal | `.ngnair.com` | HTTP-Only, Secure |
| `session_id` | Rails session | `.ngnair.com` | HTTP-Only, Secure |

### JWT Token Contents

```json
{
  "user_id": "ff4fd74e-3846-4815-9ccd-fb44f038f375",
  "email": "<EMAIL>",
  "device_id": "device-uuid-123",
  "roles": ["user", "admin"],
  "permissions": ["read", "write", "admin"],
  "exp": **********,
  "iat": **********
}
```

## 🔧 Microservice Integration

### Authentication Middleware

Each microservice should implement authentication middleware:

```ruby
class ApplicationController < ActionController::Base
  before_action :authenticate_user!
  
  private
  
  def authenticate_user!
    token = cookies.signed[:auth_token]
    
    if token.present?
      begin
        decoded_token = JWT.decode(token, Rails.application.secrets.secret_key_base)
        @current_user_id = decoded_token[0]['user_id']
        @current_user_email = decoded_token[0]['email']
        @device_id = decoded_token[0]['device_id']
        @user_roles = decoded_token[0]['roles']
        @user_permissions = decoded_token[0]['permissions']
      rescue JWT::DecodeError, JWT::ExpiredSignature
        redirect_to_auth_service
      end
    else
      redirect_to_auth_service
    end
  end
  
  def redirect_to_auth_service
    auth_url = "https://ng-auth-dev.dev1.ngnair.com/login"
    redirect_url = "#{auth_url}?redirect=#{CGI.escape(request.original_url)}"
    redirect_to redirect_url
  end
end
```

### User Context Access

```ruby
class BaseController < ApplicationController
  protected
  
  def current_user_id
    @current_user_id
  end
  
  def current_user_email
    @current_user_email
  end
  
  def current_device_id
    @device_id
  end
  
  def user_has_role?(role)
    @user_roles&.include?(role.to_s)
  end
  
  def user_has_permission?(permission)
    @user_permissions&.include?(permission.to_s)
  end
end
```

## 🔐 Security Features

### HTTP-Only Cookies
- ✅ **XSS Protection** - JavaScript cannot access auth cookies
- ✅ **Secure Flag** - Only transmitted over HTTPS
- ✅ **SameSite** - CSRF protection
- ✅ **Domain Scoped** - Shared across `*.ngnair.com` subdomains

### JWT Security
- ✅ **Cryptographically Signed** - Tamper-proof
- ✅ **Expiration** - 24-hour automatic timeout
- ✅ **Device Binding** - Tied to specific device ID
- ✅ **Role-Based Access** - Contains user permissions

### Device Tracking
- ✅ **Unique Device ID** - Per device/browser
- ✅ **Session Management** - Track active sessions
- ✅ **Security Monitoring** - Detect suspicious activity

## 🚀 Implementation Examples

### Frontend Integration

```javascript
// Redirect to auth service for login
function redirectToAuth(returnUrl) {
  const authUrl = 'https://ng-auth-dev.dev1.ngnair.com/login';
  const redirectParam = encodeURIComponent(returnUrl || window.location.href);
  window.location.href = `${authUrl}?redirect=${redirectParam}`;
}

// Check authentication status
async function checkAuth() {
  try {
    const response = await fetch('/api/auth/status', {
      credentials: 'include' // Include cookies
    });
    
    if (response.status === 401) {
      redirectToAuth();
    }
    
    return response.ok;
  } catch (error) {
    redirectToAuth();
    return false;
  }
}
```

### API Service Calls

```ruby
# Service-to-service authentication
class ApiService
  def self.call_external_service(endpoint, data = {})
    headers = {
      'Authorization' => "Bearer #{current_jwt_token}",
      'X-Device-ID' => current_device_id,
      'Content-Type' => 'application/json'
    }
    
    HTTParty.post(endpoint, {
      body: data.to_json,
      headers: headers
    })
  end
end
```

## 🔄 Session Management

### Token Refresh

```ruby
# Automatic token refresh before expiration
def refresh_token_if_needed
  token = cookies.signed[:auth_token]
  
  if token.present?
    decoded = JWT.decode(token, Rails.application.secrets.secret_key_base)
    exp_time = Time.at(decoded[0]['exp'])
    
    # Refresh if token expires within 1 hour
    if exp_time < 1.hour.from_now
      new_token = generate_new_jwt_token(decoded[0])
      set_auth_cookies(new_token)
    end
  end
rescue JWT::ExpiredSignature
  redirect_to_auth_service
end
```

### Logout Implementation

```ruby
def logout
  # Clear all authentication cookies
  cookies.delete(:auth_token, domain: '.ngnair.com')
  cookies.delete(:device_id, domain: '.ngnair.com')
  cookies.delete(:refresh_token, domain: '.ngnair.com')
  
  # Invalidate device session in database
  UserDevice.where(device_id: params[:device_id]).update(active: false)
  
  # Redirect to auth service
  redirect_to 'https://ng-auth-dev.dev1.ngnair.com/login'
end
```

## 📋 Integration Checklist

### For New Microservices

- [ ] Implement authentication middleware
- [ ] Configure cookie domain to `.ngnair.com`
- [ ] Add JWT validation logic
- [ ] Implement redirect to auth service
- [ ] Add logout functionality
- [ ] Test cross-domain cookie sharing
- [ ] Verify redirect flow works
- [ ] Test token expiration handling

### Security Checklist

- [ ] All cookies are HTTP-Only
- [ ] Secure flag enabled for production
- [ ] SameSite attribute configured
- [ ] JWT secret key properly secured
- [ ] Token expiration implemented
- [ ] Device ID validation
- [ ] HTTPS enforced
- [ ] CSRF protection enabled

## 🎯 Benefits

### Single Sign-On (SSO)
- ✅ **One Login** - Access all microservices
- ✅ **Seamless Experience** - No repeated authentication
- ✅ **Centralized Management** - Single auth service

### Security
- ✅ **HTTP-Only Cookies** - XSS protection
- ✅ **JWT Tokens** - Stateless authentication
- ✅ **Device Tracking** - Enhanced security
- ✅ **Automatic Expiration** - Session timeout

### Developer Experience
- ✅ **Easy Integration** - Simple middleware setup
- ✅ **Consistent API** - Same auth pattern across services
- ✅ **Debugging Support** - Clear error handling
- ✅ **Documentation** - Comprehensive guides

## 🐛 Troubleshooting

### Common Issues

#### 1. Authentication Loops
**Problem:** User keeps getting redirected to login
**Solution:** Check JWT token validation and cookie domain settings

```ruby
# Verify cookie domain is set correctly
cookies.signed[:auth_token] = {
  value: jwt_token,
  domain: '.ngnair.com',  # Must include the dot
  httponly: true,
  secure: true
}
```

#### 2. Cross-Domain Cookie Issues
**Problem:** Cookies not shared between microservices
**Solution:** Ensure all services use the same domain scope

```ruby
# All microservices must use same domain
COOKIE_DOMAIN = '.ngnair.com'
```

#### 3. JWT Validation Errors
**Problem:** Token validation fails
**Solution:** Verify secret key consistency across services

```ruby
# Same secret key across all microservices
JWT.decode(token, Rails.application.secrets.secret_key_base)
```

### Debug Commands

```bash
# Check cookies in browser console
document.cookie

# Decode JWT token (for debugging only)
atob(token.split('.')[1])

# Test authentication endpoint
curl -X POST https://ng-auth-dev.dev1.ngnair.com/api/v1/login \
  -H "Content-Type: application/json" \
  -H "X-Device-ID: test-device" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

## 📊 Monitoring & Analytics

### Authentication Metrics

Track these key metrics across your microservices:

- **Login Success Rate** - Successful vs failed logins
- **Session Duration** - Average user session length
- **Token Refresh Rate** - How often tokens are refreshed
- **Cross-Service Navigation** - User flow between microservices
- **Device Distribution** - Unique devices per user

### Security Monitoring

```ruby
# Log authentication events
class AuthenticationLogger
  def self.log_login(user_id, device_id, ip_address, success)
    Rails.logger.info({
      event: 'user_login',
      user_id: user_id,
      device_id: device_id,
      ip_address: ip_address,
      success: success,
      timestamp: Time.current
    }.to_json)
  end

  def self.log_suspicious_activity(user_id, device_id, reason)
    Rails.logger.warn({
      event: 'suspicious_activity',
      user_id: user_id,
      device_id: device_id,
      reason: reason,
      timestamp: Time.current
    }.to_json)
  end
end
```

## 🔄 Migration Guide

### From Session-Based to JWT

If migrating from Rails session-based auth:

1. **Phase 1:** Implement JWT alongside sessions
2. **Phase 2:** Update microservices to use JWT
3. **Phase 3:** Remove session dependencies
4. **Phase 4:** Full JWT implementation

```ruby
# Hybrid approach during migration
def authenticate_user!
  # Try JWT first
  if jwt_token_present?
    authenticate_with_jwt
  elsif session_present?
    authenticate_with_session
  else
    redirect_to_auth_service
  end
end
```

## 🚀 Performance Optimization

### Caching Strategies

```ruby
# Cache user data to reduce JWT decoding
class UserCache
  def self.get_user_data(user_id)
    Rails.cache.fetch("user_data_#{user_id}", expires_in: 15.minutes) do
      User.find(user_id).as_json(only: [:id, :email, :roles, :permissions])
    end
  end
end
```

### Token Optimization

```ruby
# Minimize JWT payload size
def generate_jwt_token(user)
  payload = {
    uid: user.id,           # Use short keys
    email: user.email,
    did: device_id,         # device_id shortened
    roles: user.roles.pluck(:name),
    exp: 24.hours.from_now.to_i
  }

  JWT.encode(payload, Rails.application.secrets.secret_key_base)
end
```

---

## 📞 Support

For integration support or questions:
- **Documentation:** This guide
- **Auth Service:** `ng-auth-dev.dev1.ngnair.com`
- **Fallback URL:** `ng-accounts-fe-dev.dev1.ngnair.com`
- **GitHub Issues:** Create issues for bugs or feature requests
- **Team Contact:** Development team for implementation support
