# === Stage 1: Builder ===
FROM node:20-alpine AS builder

WORKDIR /app

# Install deps only
COPY package.json package-lock.json ./
RUN npm ci

# Copy full source
COPY . .

# Set default build-time environment variables
ARG NEXT_PUBLIC_APP_ENV=development
ARG NEXT_PUBLIC_GRAPHQL_URL=http://localhost:3001/graphql
ARG AUTH_URL=http://localhost:3001/api/auth
ARG NEXT_PUBLIC_COOKIE_DOMAIN=.ngnair.com
ARG NEXT_PUBLIC_TRUSTED_DOMAINS=ng-*-fe-*.dev*.ngnair.com,ng-*.ng-*.ngnair.com,ng-*.dev*.ngnair.com,ng-*-fe-*.ngnair.com,*.ngnair.com,ngnair.com,localhost,127.0.0.1,localhost:3000,localhost:3001,localhost:3002
ARG NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts-fe-dev.dev1.ngnair.com
ARG NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
ARG NEXT_PUBLIC_APP_URL=http://localhost:3000

# Set environment variables for build
ENV NEXT_PUBLIC_APP_ENV=$NEXT_PUBLIC_APP_ENV
ENV NEXT_PUBLIC_GRAPHQL_URL=$NEXT_PUBLIC_GRAPHQL_URL
ENV AUTH_URL=$AUTH_URL
ENV NEXT_PUBLIC_COOKIE_DOMAIN=$NEXT_PUBLIC_COOKIE_DOMAIN
ENV NEXT_PUBLIC_TRUSTED_DOMAINS=$NEXT_PUBLIC_TRUSTED_DOMAINS
ENV NEXT_PUBLIC_FALLBACK_URL=$NEXT_PUBLIC_FALLBACK_URL
ENV NEXT_PUBLIC_AUTH_SERVICE_URL=$NEXT_PUBLIC_AUTH_SERVICE_URL
ENV NEXT_PUBLIC_APP_URL=$NEXT_PUBLIC_APP_URL

# Build the Next.js app
RUN npm run build

# === Stage 2: Production Image ===
FROM node:20-alpine AS runner

WORKDIR /app

ENV NODE_ENV=production

# Set default runtime environment variables (can be overridden)
ENV NEXT_PUBLIC_APP_ENV=development
ENV NEXT_PUBLIC_GRAPHQL_URL=http://localhost:3001/graphql
ENV AUTH_URL=http://localhost:3001/api/auth
ENV NEXT_PUBLIC_COOKIE_DOMAIN=.ngnair.com
ENV NEXT_PUBLIC_TRUSTED_DOMAINS=ng-*-fe-*.dev*.ngnair.com,ng-*.ng-*.ngnair.com,ng-*.dev*.ngnair.com,ng-*-fe-*.ngnair.com,*.ngnair.com,ngnair.com,localhost,127.0.0.1,localhost:3000,localhost:3001,localhost:3002
ENV NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts-fe-dev.dev1.ngnair.com
ENV NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
ENV NEXT_PUBLIC_APP_URL=http://localhost:3000

# Copy the minimal production artifacts from the builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

EXPOSE 3000

# Start the app
CMD ["npm", "start"]