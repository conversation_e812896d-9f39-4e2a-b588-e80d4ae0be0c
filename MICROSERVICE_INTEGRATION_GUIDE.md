# Microservice Integration Guide

## 🔄 Single Sign-On (SSO) Integration

This guide explains how to integrate your microservices with the NGNair authentication service for seamless Single Sign-On experience.

## 🏗️ Architecture Overview

### Authentication Service
- **Domain:** `ng-auth-dev.dev1.ngnair.com`
- **Purpose:** Centralized authentication for all microservices
- **Technology:** Next.js frontend + Ruby on Rails backend

### Microservices Pattern
- **Support:** `ng-support-fe-dev.dev1.ngnair.com`
- **Accounts:** `ng-accounts-fe-dev.dev1.ngnair.com`
- **Billing:** `ng-billing-fe-dev.dev1.ngnair.com`
- **Pattern:** `ng-*-fe-dev.dev1.ngnair.com`

## 🔄 SSO Flow

### Complete Authentication Flow

```
1. User visits microservice
   ↓
2. Microservice checks authentication
   ↓
3. If not authenticated → Redirect to auth service
   ↓
4. User logs in at auth service
   ↓
5. Auth service redirects back to original microservice
   ↓
6. User accesses original page with authentication
```

### URL Flow Example

```
User visits:
https://ng-support-fe-dev.dev1.ngnair.com/dashboard

↓ (not authenticated)

Redirects to:
https://ng-auth-dev.dev1.ngnair.com/login?redirect=https%3A//ng-support-fe-dev.dev1.ngnair.com/dashboard

↓ (after successful login)

Returns to:
https://ng-support-fe-dev.dev1.ngnair.com/dashboard
```

## 🛠️ Ruby on Rails Integration

### 1. Authentication Middleware

Add this to your microservice's `ApplicationController`:

```ruby
# app/controllers/application_controller.rb
class ApplicationController < ActionController::Base
  before_action :authenticate_user!
  
  private
  
  def authenticate_user!
    token = cookies.signed[:auth_token]
    
    if token.present?
      begin
        decoded_token = JWT.decode(token, Rails.application.secrets.secret_key_base)
        @current_user_id = decoded_token[0]['user_id']
        @current_user_email = decoded_token[0]['email']
        @device_id = decoded_token[0]['device_id']
        @user_roles = decoded_token[0]['roles']
        @user_permissions = decoded_token[0]['permissions']
      rescue JWT::DecodeError, JWT::ExpiredSignature
        redirect_to_auth_service
      end
    else
      redirect_to_auth_service
    end
  end
  
  def redirect_to_auth_service
    return_url = CGI.escape(request.original_url)
    auth_service_url = ENV['AUTH_SERVICE_URL'] || 'https://ng-auth-dev.dev1.ngnair.com'
    auth_url = "#{auth_service_url}/login?redirect=#{return_url}"
    redirect_to auth_url
  end
  
  # Helper methods for accessing user data
  def current_user_id
    @current_user_id
  end
  
  def current_user_email
    @current_user_email
  end
  
  def current_device_id
    @device_id
  end
  
  def user_has_role?(role)
    @user_roles&.include?(role.to_s)
  end
  
  def user_has_permission?(permission)
    @user_permissions&.include?(permission.to_s)
  end
end
```

### 2. Gemfile Dependencies

```ruby
# Gemfile
gem 'jwt'
```

### 3. Cookie Configuration

```ruby
# config/application.rb
module YourMicroservice
  class Application < Rails::Application
    # Cookie configuration for cross-domain authentication
    config.session_store :cookie_store,
      key: '_your_microservice_session',
      domain: '.ngnair.com',  # IMPORTANT: Same domain as auth service
      secure: Rails.env.production?,
      httponly: true,
      same_site: :lax
  end
end
```

### 4. Environment Configuration

```ruby
# config/environments/production.rb
Rails.application.configure do
  # Force HTTPS for secure cookies
  config.force_ssl = true
  
  # Ensure cookies work across subdomains
  config.session_store :cookie_store,
    domain: '.ngnair.com',
    secure: true
end

# config/environments/development.rb
Rails.application.configure do
  # Development settings
  config.session_store :cookie_store,
    domain: '.ngnair.com',
    secure: false  # Allow HTTP in development
end
```

### 5. Logout Implementation

```ruby
# app/controllers/sessions_controller.rb
class SessionsController < ApplicationController
  skip_before_action :authenticate_user!, only: [:destroy]
  
  def destroy
    # Clear all authentication cookies
    cookies.delete(:auth_token, domain: '.ngnair.com')
    cookies.delete(:device_id, domain: '.ngnair.com')
    cookies.delete(:refresh_token, domain: '.ngnair.com')
    
    # Redirect to auth service
    redirect_to 'https://ng-auth-dev.dev1.ngnair.com/login'
  end
end
```

### 6. Controller Usage Examples

```ruby
# app/controllers/dashboard_controller.rb
class DashboardController < ApplicationController
  def index
    # User is automatically authenticated by middleware
    @user_email = current_user_email
    @user_id = current_user_id
    
    # Role-based access control
    if user_has_role?('admin')
      @admin_data = load_admin_data
    end
    
    # Permission-based access control
    if user_has_permission?('read_reports')
      @reports = load_reports
    end
  end
  
  private
  
  def load_admin_data
    # Load admin-specific data
  end
  
  def load_reports
    # Load reports for users with permission
  end
end
```

## ⚛️ Next.js/React Integration

### 1. Authentication Hook

```javascript
// hooks/useAuth.js
import { useEffect, useState } from 'react';

export function useAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    checkAuth();
  }, []);
  
  async function checkAuth() {
    try {
      const response = await fetch('/api/auth/verify', {
        credentials: 'include' // Include HTTP-only cookies
      });
      
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        setIsAuthenticated(true);
      } else {
        redirectToAuth();
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      redirectToAuth();
    } finally {
      setLoading(false);
    }
  }
  
  function redirectToAuth() {
    const currentUrl = encodeURIComponent(window.location.href);
    const authServiceUrl = process.env.NEXT_PUBLIC_AUTH_SERVICE_URL || 'https://ng-auth-dev.dev1.ngnair.com';
    const authUrl = `${authServiceUrl}/login?redirect=${currentUrl}`;
    window.location.href = authUrl;
  }
  
  function logout() {
    // Clear any client-side data
    setUser(null);
    setIsAuthenticated(false);
    
    // Redirect to logout endpoint
    window.location.href = '/api/auth/logout';
  }
  
  return { 
    isAuthenticated, 
    loading, 
    user, 
    logout 
  };
}
```

### 2. Protected Route Component

```javascript
// components/ProtectedRoute.js
import { useAuth } from '../hooks/useAuth';

export function ProtectedRoute({ children, requiredRole = null, requiredPermission = null }) {
  const { isAuthenticated, loading, user } = useAuth();
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  if (!isAuthenticated) {
    return null; // useAuth will handle redirect
  }
  
  // Role-based access control
  if (requiredRole && !user?.roles?.includes(requiredRole)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
          <p>You don't have the required role: {requiredRole}</p>
        </div>
      </div>
    );
  }
  
  // Permission-based access control
  if (requiredPermission && !user?.permissions?.includes(requiredPermission)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
          <p>You don't have the required permission: {requiredPermission}</p>
        </div>
      </div>
    );
  }
  
  return children;
}
```

### 3. API Route for Authentication Verification

```javascript
// pages/api/auth/verify.js (or app/api/auth/verify/route.js for App Router)
import jwt from 'jsonwebtoken';

export default function handler(req, res) {
  try {
    const token = req.cookies.auth_token;
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Return user data
    res.status(200).json({
      user_id: decoded.user_id,
      email: decoded.email,
      device_id: decoded.device_id,
      roles: decoded.roles,
      permissions: decoded.permissions
    });
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
}
```

### 4. Logout API Route

```javascript
// pages/api/auth/logout.js
export default function handler(req, res) {
  // Clear cookies
  res.setHeader('Set-Cookie', [
    'auth_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Domain=.ngnair.com',
    'device_id=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Domain=.ngnair.com'
  ]);
  
  // Redirect to auth service
  res.redirect('https://ng-auth-dev.dev1.ngnair.com/login');
}
```

### 5. Page Usage Examples

```javascript
// pages/dashboard.js
import { ProtectedRoute } from '../components/ProtectedRoute';
import { useAuth } from '../hooks/useAuth';

export default function Dashboard() {
  const { user, logout } = useAuth();
  
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold">Dashboard</h1>
              </div>
              <div className="flex items-center space-x-4">
                <span>Welcome, {user?.email}</span>
                <button 
                  onClick={logout}
                  className="bg-red-500 text-white px-4 py-2 rounded"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </header>
        
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <h2>Dashboard Content</h2>
            <p>User ID: {user?.user_id}</p>
            <p>Device ID: {user?.device_id}</p>
            <p>Roles: {user?.roles?.join(', ')}</p>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
```

### 6. Admin-Only Page Example

```javascript
// pages/admin.js
import { ProtectedRoute } from '../components/ProtectedRoute';

export default function AdminPage() {
  return (
    <ProtectedRoute requiredRole="admin">
      <div>
        <h1>Admin Dashboard</h1>
        <p>This page is only accessible to users with admin role.</p>
      </div>
    </ProtectedRoute>
  );
}
```

## 🔧 Configuration Checklist

### For Each New Microservice

#### Ruby on Rails Microservices:
- [ ] Add `gem 'jwt'` to Gemfile
- [ ] Implement authentication middleware in ApplicationController
- [ ] Configure cookie domain to `.ngnair.com`
- [ ] Set up JWT secret key (same as auth service)
- [ ] Add logout route and controller
- [ ] Configure HTTPS for production
- [ ] Test redirect flow

#### Next.js/React Microservices:
- [ ] Install `jsonwebtoken` package
- [ ] Create authentication hook (`useAuth`)
- [ ] Create protected route component
- [ ] Add API routes for auth verification and logout
- [ ] Configure cookie domain in API routes
- [ ] Test authentication flow
- [ ] Implement role/permission-based access control

### Environment Variables

```bash
# .env (for all microservices)
JWT_SECRET=your_shared_jwt_secret_key
AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
COOKIE_DOMAIN=.ngnair.com
FALLBACK_URL=https://ng-accounts-fe-dev.dev1.ngnair.com

# For Next.js/React microservices, use NEXT_PUBLIC_ prefix
NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts-fe-dev.dev1.ngnair.com
NEXT_PUBLIC_COOKIE_DOMAIN=.ngnair.com
```

## 🧪 Testing Integration

### Manual Testing Steps

1. **Visit microservice without authentication**
   - Should redirect to auth service with correct redirect URL

2. **Complete login flow**
   - Should return to original microservice page

3. **Navigate between microservices**
   - Should not require re-authentication

4. **Test logout**
   - Should clear cookies and redirect to auth service

5. **Test token expiration**
   - Should redirect to auth service when token expires

### Test URLs

```bash
# Test these URLs in your browser
https://ng-support-fe-dev.dev1.ngnair.com/dashboard
https://ng-accounts-fe-dev.dev1.ngnair.com/profile
https://ng-billing-fe-dev.dev1.ngnair.com/invoices

# Each should redirect to:
https://ng-auth-dev.dev1.ngnair.com/login?redirect=<original_url>
```

## 🚨 Common Issues & Solutions

### Issue 1: Infinite Redirect Loop
**Problem:** Microservice keeps redirecting to auth service
**Solution:** Check JWT secret key consistency and cookie domain configuration

### Issue 2: Cookies Not Shared
**Problem:** Authentication doesn't persist across microservices
**Solution:** Ensure all services use domain `.ngnair.com` (with the dot)

### Issue 3: HTTPS/HTTP Mismatch
**Problem:** Cookies not working in production
**Solution:** Ensure all services use HTTPS in production

### Issue 4: Token Validation Fails
**Problem:** JWT decode errors
**Solution:** Verify all microservices use the same JWT secret key

## 🔄 Advanced Integration Patterns

### Service-to-Service Authentication

For backend services that need to communicate with each other:

```ruby
# Service-to-service API calls with JWT
class ApiService
  def self.call_external_service(endpoint, data = {})
    token = current_jwt_token

    headers = {
      'Authorization' => "Bearer #{token}",
      'X-Device-ID' => current_device_id,
      'Content-Type' => 'application/json'
    }

    HTTParty.post(endpoint, {
      body: data.to_json,
      headers: headers,
      timeout: 30
    })
  end

  private

  def self.current_jwt_token
    # Get token from current request context
    RequestStore.store[:current_jwt_token]
  end

  def self.current_device_id
    RequestStore.store[:current_device_id]
  end
end
```

### Middleware for API-Only Services

```ruby
# For API-only microservices (no web interface)
class ApiAuthenticationMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    request = Rack::Request.new(env)

    # Extract JWT from Authorization header
    auth_header = request.get_header('HTTP_AUTHORIZATION')
    token = auth_header&.sub(/^Bearer /, '')

    if token.present?
      begin
        decoded_token = JWT.decode(token, Rails.application.secrets.secret_key_base)

        # Store user context for the request
        RequestStore.store[:current_user_id] = decoded_token[0]['user_id']
        RequestStore.store[:current_device_id] = decoded_token[0]['device_id']
        RequestStore.store[:current_jwt_token] = token

        @app.call(env)
      rescue JWT::DecodeError, JWT::ExpiredSignature
        [401, {'Content-Type' => 'application/json'}, [{ error: 'Unauthorized' }.to_json]]
      end
    else
      [401, {'Content-Type' => 'application/json'}, [{ error: 'No token provided' }.to_json]]
    end
  end
end

# config/application.rb
config.middleware.use ApiAuthenticationMiddleware
```

### GraphQL Integration

```javascript
// For GraphQL microservices
import { ApolloServer } from 'apollo-server-express';
import jwt from 'jsonwebtoken';

const server = new ApolloServer({
  typeDefs,
  resolvers,
  context: ({ req }) => {
    // Extract user from JWT token
    const token = req.cookies.auth_token;
    let user = null;

    if (token) {
      try {
        user = jwt.verify(token, process.env.JWT_SECRET);
      } catch (error) {
        // Token invalid, user remains null
      }
    }

    return {
      user,
      isAuthenticated: !!user
    };
  }
});
```

## 🔒 Security Best Practices

### 1. Token Validation

```ruby
# Enhanced JWT validation with additional security checks
def validate_jwt_token(token)
  begin
    decoded_token = JWT.decode(
      token,
      Rails.application.secrets.secret_key_base,
      true, # Verify signature
      {
        algorithm: 'HS256',
        verify_expiration: true,
        verify_iat: true
      }
    )

    payload = decoded_token[0]

    # Additional security checks
    return false if payload['exp'] < Time.current.to_i
    return false if payload['iat'] > Time.current.to_i
    return false unless valid_device_id?(payload['device_id'])

    payload
  rescue JWT::DecodeError, JWT::ExpiredSignature, JWT::InvalidIatError
    false
  end
end

def valid_device_id?(device_id)
  # Check if device_id is still active in database
  UserDevice.where(device_id: device_id, active: true).exists?
end
```

### 2. Rate Limiting

```ruby
# Add rate limiting to prevent abuse
class RateLimitMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    request = Rack::Request.new(env)

    # Rate limit by IP and user_id
    ip = request.ip
    user_id = extract_user_id_from_request(request)

    if rate_limit_exceeded?(ip, user_id)
      return [429, {'Content-Type' => 'application/json'},
              [{ error: 'Rate limit exceeded' }.to_json]]
    end

    @app.call(env)
  end

  private

  def rate_limit_exceeded?(ip, user_id)
    # Implement rate limiting logic
    # e.g., using Redis to track request counts
  end
end
```

### 3. CORS Configuration

```ruby
# config/initializers/cors.rb
Rails.application.config.middleware.insert_before 0, Rack::Cors do
  allow do
    origins 'ng-auth-dev.dev1.ngnair.com',
            'ng-support-fe-dev.dev1.ngnair.com',
            'ng-accounts-fe-dev.dev1.ngnair.com',
            /\Ang-.*\.dev1\.ngnair\.com\z/ # Allow all ng-* subdomains

    resource '*',
      headers: :any,
      methods: [:get, :post, :put, :patch, :delete, :options, :head],
      credentials: true # Important for cookies
  end
end
```

## 📊 Monitoring & Logging

### Authentication Event Logging

```ruby
# Log all authentication events for monitoring
class AuthenticationLogger
  def self.log_authentication_attempt(user_id, device_id, ip_address, success, microservice)
    Rails.logger.info({
      event: 'authentication_attempt',
      user_id: user_id,
      device_id: device_id,
      ip_address: ip_address,
      success: success,
      microservice: microservice,
      timestamp: Time.current.iso8601
    }.to_json)
  end

  def self.log_cross_service_navigation(user_id, from_service, to_service)
    Rails.logger.info({
      event: 'cross_service_navigation',
      user_id: user_id,
      from_service: from_service,
      to_service: to_service,
      timestamp: Time.current.iso8601
    }.to_json)
  end

  def self.log_token_refresh(user_id, device_id)
    Rails.logger.info({
      event: 'token_refresh',
      user_id: user_id,
      device_id: device_id,
      timestamp: Time.current.iso8601
    }.to_json)
  end
end
```

### Health Check Endpoints

```ruby
# Add health check endpoints for monitoring
class HealthController < ApplicationController
  skip_before_action :authenticate_user!

  def auth_status
    # Check if authentication service is reachable
    begin
      response = HTTParty.get('https://ng-auth-dev.dev1.ngnair.com/health', timeout: 5)

      if response.success?
        render json: { status: 'healthy', auth_service: 'reachable' }
      else
        render json: { status: 'degraded', auth_service: 'unreachable' }, status: 503
      end
    rescue => e
      render json: { status: 'unhealthy', error: e.message }, status: 503
    end
  end

  def jwt_validation
    # Test JWT validation
    test_token = generate_test_jwt

    if validate_jwt_token(test_token)
      render json: { status: 'healthy', jwt_validation: 'working' }
    else
      render json: { status: 'unhealthy', jwt_validation: 'failed' }, status: 503
    end
  end
end
```

## 🚀 Deployment Considerations

### Environment-Specific Configuration

```yaml
# docker-compose.yml
version: '3.8'
services:
  microservice:
    environment:
      - JWT_SECRET=${JWT_SECRET}
      - AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
      - COOKIE_DOMAIN=.ngnair.com
      - RAILS_ENV=production
    networks:
      - ngnair-network

networks:
  ngnair-network:
    external: true
```

### Load Balancer Configuration

```nginx
# nginx.conf for load balancer
upstream auth_service {
    server ng-auth-dev.dev1.ngnair.com;
}

upstream support_service {
    server ng-support-fe-dev.dev1.ngnair.com;
}

server {
    listen 443 ssl;
    server_name *.ngnair.com;

    # SSL configuration
    ssl_certificate /path/to/wildcard.ngnair.com.crt;
    ssl_certificate_key /path/to/wildcard.ngnair.com.key;

    # Preserve original host for redirects
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    location / {
        # Route based on subdomain
        if ($host ~* "^ng-auth-") {
            proxy_pass https://auth_service;
        }
        if ($host ~* "^ng-support-") {
            proxy_pass https://support_service;
        }
        # Add more routing rules as needed
    }
}
```

## 📞 Support

For integration support:
- **Auth Service:** `ng-auth-dev.dev1.ngnair.com`
- **Documentation:** This guide + `AUTHENTICATION_MICROSERVICES_GUIDE.md`
- **Fallback URL:** `ng-accounts-fe-dev.dev1.ngnair.com`
- **Health Checks:** `/health` endpoint on each microservice
- **Monitoring:** Check authentication logs for integration issues
