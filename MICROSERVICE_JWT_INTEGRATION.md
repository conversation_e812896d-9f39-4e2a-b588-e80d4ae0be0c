# 🔐 Microservice JWT Integration Guide

## 📋 Overview

This guide explains how microservices should validate JWT tokens using the JWKS (JSON Web Key Set) endpoint instead of making validation requests to the auth service.

## 🔑 JWT Token Structure

The JWT tokens contain user context with accounts and partners:

```json
{
  "sub": "user_123",
  "iss": "auth.ngnair.com", 
  "iat": **********,
  "exp": **********,
  "accounts": [
    {
      "id": "acct_001",
      "role": "admin",
      "scopes": ["account:manage", "transaction:refund"]
    },
    {
      "id": "acct_002", 
      "role": "viewer",
      "scopes": ["account:view"]
    }
  ],
  "partners": [
    {
      "id": "partner_123",
      "role": "agent", 
      "scopes": ["partner:view_accounts"]
    },
    {
      "id": "partner_456",
      "role": "admin",
      "scopes": ["partner:assign", "finance:view"]
    }
  ],
  "selected_account_id": "acct_001",
  "selected_partner_id": null
}
```

## 🛠️ Frontend Integration

### 1. Using JWT Utils (Recommended)

```typescript
import { JWTUtils } from '@/lib/jwt-utils';

// Check if user is authenticated
const isAuthenticated = !JWTUtils.isTokenExpired();

// Get user context
const userContext = JWTUtils.getUserContext();
console.log('User ID:', userContext.userId);
console.log('Selected Account:', userContext.selectedAccountId);

// Check permissions
const canManageAccount = JWTUtils.hasAccountScope('acct_001', 'account:manage');
const canViewPartner = JWTUtils.hasPartnerScope('partner_123', 'partner:view_accounts');

// Check if user has any admin scopes
const hasAdminAccess = JWTUtils.hasAnyAccountScope(['account:manage', 'transaction:refund']);

// Redirect to auth if needed
if (!isAuthenticated) {
  JWTUtils.redirectToAuth();
}
```

### 2. React Hook for Authentication

```typescript
// hooks/useAuth.ts
import { useState, useEffect } from 'react';
import { JWTUtils } from '@/lib/jwt-utils';

export function useAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userContext, setUserContext] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      const authenticated = !JWTUtils.isTokenExpired();
      setIsAuthenticated(authenticated);
      
      if (authenticated) {
        setUserContext(JWTUtils.getUserContext());
      } else {
        setUserContext(null);
      }
      
      setLoading(false);
    };

    checkAuth();
    
    // Check every minute for token expiration
    const interval = setInterval(checkAuth, 60000);
    
    return () => clearInterval(interval);
  }, []);

  const logout = () => {
    JWTUtils.clearAuth();
    setIsAuthenticated(false);
    setUserContext(null);
  };

  const redirectToAuth = (returnUrl?: string) => {
    JWTUtils.redirectToAuth(returnUrl);
  };

  return {
    isAuthenticated,
    userContext,
    loading,
    logout,
    redirectToAuth,
  };
}
```

### 3. Protected Route Component

```typescript
// components/ProtectedRoute.tsx
import { useAuth } from '@/hooks/useAuth';
import { JWTUtils } from '@/lib/jwt-utils';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredScopes?: string[];
  accountId?: string;
  partnerId?: string;
}

export function ProtectedRoute({ 
  children, 
  requiredScopes = [], 
  accountId, 
  partnerId 
}: ProtectedRouteProps) {
  const { isAuthenticated, userContext, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    JWTUtils.redirectToAuth();
    return <div>Redirecting to login...</div>;
  }

  // Check scope permissions
  if (requiredScopes.length > 0) {
    let hasPermission = false;

    if (accountId) {
      hasPermission = requiredScopes.some(scope => 
        JWTUtils.hasAccountScope(accountId, scope)
      );
    } else if (partnerId) {
      hasPermission = requiredScopes.some(scope => 
        JWTUtils.hasPartnerScope(partnerId, scope)
      );
    } else {
      // Check against selected account/partner
      hasPermission = JWTUtils.hasAnyAccountScope(requiredScopes) || 
                     JWTUtils.hasAnyPartnerScope(requiredScopes);
    }

    if (!hasPermission) {
      return <div>Access denied. Insufficient permissions.</div>;
    }
  }

  return <>{children}</>;
}
```

## 🖥️ Backend Integration (Node.js/Express)

### 1. JWT Validation Middleware

```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');

const client = jwksClient({
  jwksUri: 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
  cache: true,
  cacheMaxAge: 3600000, // 1 hour
});

function getKey(header, callback) {
  client.getSigningKey(header.kid, (err, key) => {
    if (err) {
      return callback(err);
    }
    const signingKey = key.publicKey || key.rsaPublicKey;
    callback(null, signingKey);
  });
}

function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, getKey, {
    audience: 'ngnair-microservices',
    issuer: 'auth.ngnair.com',
    algorithms: ['RS256']
  }, (err, decoded) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    
    req.user = decoded;
    next();
  });
}

// Scope checking middleware
function requireScope(scope, accountId = null, partnerId = null) {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    let hasScope = false;

    // Check account scopes
    if (accountId || user.selected_account_id) {
      const targetAccountId = accountId || user.selected_account_id;
      const account = user.accounts?.find(acc => acc.id === targetAccountId);
      if (account && account.scopes.includes(scope)) {
        hasScope = true;
      }
    }

    // Check partner scopes
    if (!hasScope && (partnerId || user.selected_partner_id)) {
      const targetPartnerId = partnerId || user.selected_partner_id;
      const partner = user.partners?.find(p => p.id === targetPartnerId);
      if (partner && partner.scopes.includes(scope)) {
        hasScope = true;
      }
    }

    if (!hasScope) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
}

module.exports = { authenticateToken, requireScope };
```

### 2. Using the Middleware

```javascript
// routes/accounts.js
const express = require('express');
const { authenticateToken, requireScope } = require('../middleware/auth');
const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// View account - requires account:view scope
router.get('/:accountId', requireScope('account:view'), (req, res) => {
  const { accountId } = req.params;
  const user = req.user;
  
  // User has been authenticated and has the required scope
  res.json({
    message: `Account ${accountId} data`,
    user_id: user.sub,
    selected_account: user.selected_account_id
  });
});

// Manage account - requires account:manage scope  
router.put('/:accountId', requireScope('account:manage'), (req, res) => {
  // Handle account management
  res.json({ message: 'Account updated' });
});

module.exports = router;
```

## 🔧 Testing Your Integration

### 1. Test JWT Decoding

```javascript
// test-jwt.js
const { JWTUtils } = require('./lib/jwt-utils');

// Test with a sample token
const sampleToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...';

console.log('Token payload:', JWTUtils.decodePayload(sampleToken));
console.log('Is expired:', JWTUtils.isTokenExpired(sampleToken));
console.log('User context:', JWTUtils.getUserContext(sampleToken));
```

### 2. Test JWKS Endpoint

```bash
# Fetch JWKS
curl https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks

# Expected response:
{
  "keys": [
    {
      "kty": "RSA",
      "use": "sig", 
      "kid": "key-id-1",
      "n": "...",
      "e": "AQAB"
    }
  ]
}
```

## 🚀 Best Practices

1. **Cache JWKS**: Cache the JWKS response for at least 1 hour
2. **Validate Claims**: Always validate `iss`, `aud`, `exp`, and `iat` claims
3. **Handle Errors**: Gracefully handle token validation errors
4. **Scope Checking**: Use the most specific scope checking possible
5. **Token Refresh**: Implement token refresh logic for long-running sessions

## 🔒 Security Considerations

- Never validate tokens by calling the auth service for every request
- Always verify the JWT signature using JWKS
- Check token expiration on every request
- Use HTTPS for all communications
- Implement proper error handling to avoid information leakage

This approach provides better performance and reduces load on the auth service while maintaining security! 🎉
