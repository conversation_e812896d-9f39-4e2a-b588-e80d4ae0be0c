version: '3.8'

services:
  auth-frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      # Override environment variables here
      - NEXT_PUBLIC_APP_ENV=development
      - NEXT_PUBLIC_GRAPHQL_URL=http://localhost:3001/graphql
      - AUTH_URL=http://localhost:3001/api/auth
      - NEXT_PUBLIC_COOKIE_DOMAIN=.ngnair.com
      - NEXT_PUBLIC_TRUSTED_DOMAINS=ng-*-fe-*.dev*.ngnair.com,ng-*.ng-*.ngnair.com,ng-*.dev*.ngnair.com,ng-*-fe-*.ngnair.com,*.ngnair.com,ngnair.com,localhost,127.0.0.1,localhost:3000,localhost:3001,localhost:3002
      - NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts-fe-dev.dev1.ngnair.com
      - NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
    networks:
      - auth-network

  # Example: Production configuration
  auth-frontend-prod:
    build: .
    ports:
      - "3001:3000"
    environment:
      # Production environment variables
      - NEXT_PUBLIC_APP_ENV=production
      - NEXT_PUBLIC_GRAPHQL_URL=https://ng-auth.ngnair.com/graphql
      - AUTH_URL=https://ng-auth.ngnair.com/api/auth
      - NEXT_PUBLIC_COOKIE_DOMAIN=.ngnair.com
      - NEXT_PUBLIC_TRUSTED_DOMAINS=ng-*.ngnair.com,*.ngnair.com,ngnair.com
      - NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts.ngnair.com
      - NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth.ngnair.com
      - NEXT_PUBLIC_APP_URL=https://ng-auth.ngnair.com
    networks:
      - auth-network
    profiles:
      - production

networks:
  auth-network:
    driver: bridge
