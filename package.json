{"name": "auth-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:watch": "eslint src pages lib --ext .ts,.tsx --watch", "codegen": "graphql-codegen --config ./src/lib/graphql/codegen.ts", "env:dev": "node scripts/switch-env.js development", "env:prod": "node scripts/switch-env.js production", "build:dev": "npm run env:dev && npm run build", "build:prod": "npm run env:prod && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/experimental-nextjs-app-support": "^0.12.2", "@lifeomic/axios-fetch": "^3.1.0", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.2", "apollo-upload-client": "^18.0.1", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "graphql": "^16.11.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "next": "^15.3.2", "next-runtime-env": "^3.3.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-phone-input-2": "^2.15.1", "sass": "^1.89.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.27.0", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/client-preset": "^4.8.1", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@tailwindcss/postcss": "^4.1.7", "@types/js-cookie": "^3.0.6", "@types/jwt-decode": "^2.2.1", "@types/node": "^20.19.7", "@types/react": "^19.1.8", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-next": "15.3.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}, "description": "", "main": "postcss.config.js", "keywords": [], "author": "", "license": "ISC"}