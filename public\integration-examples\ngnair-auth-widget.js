/**
 * NGnair Authentication Widget
 * Easy iframe integration for third-party websites
 * 
 * Usage:
 * <script src="https://your-domain.com/ngnair-auth-widget.js"></script>
 * <script>
 *   NGnairAuth.init({
 *     containerId: 'auth-container',
 *     authUrl: 'https://your-auth-domain.com',
 *     onAuthChange: function(status) {
 *       console.log('Auth status:', status);
 *     }
 *   });
 * </script>
 */

(function(window) {
    'use strict';

    // Trusted domain patterns for validation
    const TRUSTED_AUTH_DOMAINS = [
        'ng-*-fe-*.dev*.ngnair.com',     // Matches ng-support-fe-dev.dev1.ngnair.com
        'ng-*.ng-*.ngnair.com',          // Matches ng-ob.ng-dev.ngnair.com
        'ng-*.dev*.ngnair.com',          // General pattern for ng-[service].dev[env].ngnair.com
        'ng-*-fe-*.ngnair.com',          // General pattern for ng-[service]-fe-[env].ngnair.com
        '*.ngnair.com',                  // All subdomains of ngnair.com
        'ngnair.com',                    // Exact match
        'localhost',                     // Development
        '127.0.0.1',                     // Local development
    ];

    // Domain validation function
    const isValidDomain = (domain, pattern) => {
        const regexPattern = pattern
            .replace(/\./g, '\\.')  // Escape dots
            .replace(/\*/g, '[^.]*'); // Replace * with non-dot characters

        const regex = new RegExp(`^${regexPattern}$`, 'i');
        return regex.test(domain);
    };

    const isTrustedAuthDomain = (origin) => {
        try {
            const url = new URL(origin);
            const domain = url.hostname;

            return TRUSTED_AUTH_DOMAINS.some(pattern => isValidDomain(domain, pattern));
        } catch (e) {
            console.warn('Invalid origin URL:', origin);
            return false;
        }
    };

    // Default configuration
    const DEFAULT_CONFIG = {
        authUrl: 'http://localhost:3000',
        containerId: 'ngnair-auth',
        width: '100%',
        height: '600px',
        onAuthChange: null,
        onReady: null,
        onError: null,
        autoResize: true,
        showBorder: true,
        borderRadius: '8px',
        debug: false,
        validateOrigin: true  // Enable origin validation by default
    };

    // NGnair Authentication Widget
    class NGnairAuthWidget {
        constructor(config = {}) {
            this.config = { ...DEFAULT_CONFIG, ...config };
            this.iframe = null;
            this.container = null;
            this.authStatus = 'unknown';
            this.isReady = false;
            
            this.init();
        }

        init() {
            this.log('Initializing NGnair Auth Widget...');
            
            // Find or create container
            this.container = document.getElementById(this.config.containerId);
            if (!this.container) {
                this.error(`Container with ID '${this.config.containerId}' not found`);
                return;
            }

            // Create iframe
            this.createIframe();
            
            // Set up message listener
            this.setupMessageListener();
            
            this.log('Widget initialized successfully');
        }

        createIframe() {
            this.iframe = document.createElement('iframe');
            this.iframe.src = `${this.config.authUrl}/login`;
            this.iframe.style.width = this.config.width;
            this.iframe.style.height = this.config.height;
            this.iframe.style.border = this.config.showBorder ? '1px solid #ddd' : 'none';
            this.iframe.style.borderRadius = this.config.borderRadius;
            this.iframe.title = 'NGnair Authentication';
            this.iframe.allow = 'camera; microphone; geolocation';
            
            // Handle iframe load
            this.iframe.addEventListener('load', () => {
                this.isReady = true;
                this.log('Iframe loaded successfully');
                
                // Request initial auth status
                setTimeout(() => {
                    this.requestAuthStatus();
                }, 1000);
                
                if (this.config.onReady) {
                    this.config.onReady();
                }
            });

            // Handle iframe errors
            this.iframe.addEventListener('error', (e) => {
                this.error('Iframe failed to load', e);
            });

            this.container.appendChild(this.iframe);
        }

        setupMessageListener() {
            window.addEventListener('message', (event) => {
                // Validate origin if enabled
                if (this.config.validateOrigin && !isTrustedAuthDomain(event.origin)) {
                    this.log(`Ignored message from untrusted origin: ${event.origin}`);
                    return;
                }

                this.log(`Received message from ${event.origin}:`, event.data);

                if (event.data?.type === 'auth-status') {
                    this.handleAuthStatusChange(event.data.status);
                }
            });
        }

        handleAuthStatusChange(status) {
            const previousStatus = this.authStatus;
            this.authStatus = status;
            
            this.log(`Auth status changed: ${previousStatus} → ${status}`);
            
            if (this.config.onAuthChange) {
                this.config.onAuthChange(status, previousStatus);
            }

            // Trigger custom event
            this.dispatchEvent('authchange', {
                status,
                previousStatus,
                timestamp: Date.now()
            });
        }

        // Public methods
        requestAuthStatus() {
            if (this.iframe && this.iframe.contentWindow && this.isReady) {
                this.iframe.contentWindow.postMessage({
                    type: 'request-auth-status'
                }, '*');
                this.log('Requested auth status');
            } else {
                this.log('Cannot request auth status - iframe not ready');
            }
        }

        getAuthStatus() {
            return this.authStatus;
        }

        isAuthenticated() {
            return this.authStatus === 'authenticated';
        }

        show() {
            if (this.container) {
                this.container.style.display = 'block';
            }
        }

        hide() {
            if (this.container) {
                this.container.style.display = 'none';
            }
        }

        destroy() {
            if (this.iframe) {
                this.iframe.remove();
                this.iframe = null;
            }
            this.isReady = false;
            this.log('Widget destroyed');
        }

        // Utility methods
        dispatchEvent(eventName, detail) {
            const event = new CustomEvent(`ngnair-${eventName}`, {
                detail,
                bubbles: true
            });
            this.container.dispatchEvent(event);
        }

        log(...args) {
            if (this.config.debug) {
                console.log('[NGnair Auth Widget]', ...args);
            }
        }

        error(...args) {
            console.error('[NGnair Auth Widget]', ...args);
            if (this.config.onError) {
                this.config.onError(...args);
            }
        }
    }

    // Global NGnairAuth object
    window.NGnairAuth = {
        // Initialize widget
        init: function(config) {
            return new NGnairAuthWidget(config);
        },

        // Create multiple widgets
        create: function(config) {
            return new NGnairAuthWidget(config);
        },

        // Add custom trusted domain patterns
        addTrustedDomain: function(pattern) {
            if (typeof pattern === 'string' && !TRUSTED_AUTH_DOMAINS.includes(pattern)) {
                TRUSTED_AUTH_DOMAINS.push(pattern);
                console.log(`Added trusted domain pattern: ${pattern}`);
            }
        },

        // Remove trusted domain pattern
        removeTrustedDomain: function(pattern) {
            const index = TRUSTED_AUTH_DOMAINS.indexOf(pattern);
            if (index > -1) {
                TRUSTED_AUTH_DOMAINS.splice(index, 1);
                console.log(`Removed trusted domain pattern: ${pattern}`);
            }
        },

        // Get current trusted domains
        getTrustedDomains: function() {
            return [...TRUSTED_AUTH_DOMAINS];
        },

        // Validate if a domain is trusted
        isTrustedDomain: function(origin) {
            return isTrustedAuthDomain(origin);
        },

        // Utility to check if browser supports required features
        isSupported: function() {
            return !!(window.postMessage && window.addEventListener && document.createElement);
        },

        // Version
        version: '1.0.0'
    };

    // Auto-initialize if data attributes are found
    document.addEventListener('DOMContentLoaded', function() {
        const autoInitElements = document.querySelectorAll('[data-ngnair-auth]');
        
        autoInitElements.forEach(element => {
            const config = {
                containerId: element.id || 'ngnair-auth-' + Math.random().toString(36).substr(2, 9),
                authUrl: element.dataset.authUrl || DEFAULT_CONFIG.authUrl,
                width: element.dataset.width || DEFAULT_CONFIG.width,
                height: element.dataset.height || DEFAULT_CONFIG.height,
                debug: element.dataset.debug === 'true'
            };

            if (!element.id) {
                element.id = config.containerId;
            }

            window.NGnairAuth.init(config);
        });
    });

})(window);
