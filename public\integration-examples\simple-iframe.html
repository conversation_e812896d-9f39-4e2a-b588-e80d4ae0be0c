<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Iframe Integration Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .auth-container {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .auth-iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>NGnair Authentication Integration Example</h1>
    
    <div class="status-display">
        <h3>Authentication Status</h3>
        <p><strong>Status:</strong> <span id="auth-status">Unknown</span></p>
        <p><strong>Last Update:</strong> <span id="last-update">Never</span></p>
        <p><strong>In Iframe:</strong> <span id="iframe-detected">Detecting...</span></p>
    </div>

    <div class="controls">
        <button onclick="requestAuthStatus()">Check Auth Status</button>
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="toggleIframe()">Toggle Iframe</button>
    </div>

    <div class="auth-container">
        <h3>Authentication Iframe</h3>
        <iframe 
            id="auth-iframe"
            src="http://localhost:3000/login"
            class="auth-iframe"
            title="NGnair Authentication"
            allow="camera; microphone; geolocation">
        </iframe>
    </div>

    <div class="log">
        <h4>Event Log:</h4>
        <div id="event-log"></div>
    </div>

    <script>
        // Authentication status tracking
        let authStatus = 'unknown';
        let iframe = document.getElementById('auth-iframe');
        
        // Log function
        function log(message) {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Listen for messages from the authentication iframe
        window.addEventListener('message', function(event) {
            // In production, verify the origin for security
            // if (event.origin !== 'https://your-auth-domain.com') return;
            
            log(`Received message from: ${event.origin}`);
            log(`Message type: ${event.data?.type}`);
            
            if (event.data?.type === 'auth-status') {
                authStatus = event.data.status;
                updateStatusDisplay(event.data.status);
                log(`Auth status changed to: ${event.data.status}`);
                
                // Handle different authentication states
                handleAuthStatusChange(event.data.status);
            }
        });

        // Update the status display
        function updateStatusDisplay(status) {
            document.getElementById('auth-status').textContent = status;
            document.getElementById('last-update').textContent = new Date().toLocaleString();
            
            // Change color based on status
            const statusElement = document.getElementById('auth-status');
            statusElement.style.color = getStatusColor(status);
        }

        // Get color for status
        function getStatusColor(status) {
            switch(status) {
                case 'authenticated': return 'green';
                case 'login-success': return 'darkgreen';
                case 'unauthenticated': return 'red';
                case 'logout': return 'orange';
                default: return 'gray';
            }
        }

        // Handle authentication status changes
        function handleAuthStatusChange(status) {
            switch(status) {
                case 'authenticated':
                    log('✅ User is authenticated');
                    // Enable authenticated features
                    break;
                    
                case 'login-success':
                    log('🎉 User just logged in successfully');
                    // Maybe redirect or show welcome message
                    // window.location.href = '/dashboard';
                    break;
                    
                case 'unauthenticated':
                    log('❌ User is not authenticated');
                    // Show login prompt or disable features
                    break;
                    
                case 'logout':
                    log('👋 User logged out');
                    // Clear user data, redirect to public page
                    break;
                    
                default:
                    log(`ℹ️ Unknown status: ${status}`);
            }
        }

        // Request current authentication status
        function requestAuthStatus() {
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({ 
                    type: 'request-auth-status' 
                }, '*'); // In production, use specific origin
                log('📤 Requested auth status from iframe');
            } else {
                log('❌ Iframe not available');
            }
        }

        // Clear the event log
        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
        }

        // Toggle iframe visibility
        function toggleIframe() {
            const container = document.querySelector('.auth-container');
            container.style.display = container.style.display === 'none' ? 'block' : 'none';
        }

        // Detect if we're in an iframe
        function detectIframe() {
            const inIframe = window.self !== window.top;
            document.getElementById('iframe-detected').textContent = inIframe ? 'Yes' : 'No';
            return inIframe;
        }

        // Initialize
        window.addEventListener('load', function() {
            log('🚀 Page loaded, waiting for iframe...');
            detectIframe();
            
            // Request initial status after iframe loads
            setTimeout(() => {
                requestAuthStatus();
            }, 2000);
        });

        // Handle iframe load
        iframe.addEventListener('load', function() {
            log('📱 Iframe loaded successfully');
            setTimeout(() => {
                requestAuthStatus();
            }, 1000);
        });
    </script>
</body>
</html>
