<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trusted Domain Integration Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .code {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .trusted { background: #d4edda; color: #155724; }
        .blocked { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            align-items: center;
        }
    </style>
</head>
<body>
    <h1>NGnair Authentication - Trusted Domain Security</h1>
    
    <div class="status info">
        <strong>Security Feature:</strong> This authentication system only accepts iframe communication from trusted domains using wildcard patterns like <code>ng-*.com</code>.
    </div>

    <!-- Configuration Example -->
    <div class="example-section">
        <h2>1. Server Configuration</h2>
        <p>Configure trusted domains in your authentication server's environment:</p>
        
        <div class="code">
# .env.local (Authentication Server)
NEXT_PUBLIC_TRUSTED_DOMAINS=ng-*-fe-*.dev*.ngnair.com,ng-*.ng-*.ngnair.com,*.ngnair.com,ngnair.com,localhost,127.0.0.1

# This allows:
# ✅ ng-support-fe-dev.dev1.ngnair.com (ng-*-fe-*.dev*.ngnair.com pattern)
# ✅ ng-ob.ng-dev.ngnair.com (ng-*.ng-*.ngnair.com pattern)
# ✅ app.ngnair.com, api.ngnair.com (*.ngnair.com pattern)
# ✅ ngnair.com (exact match)
# ✅ localhost, 127.0.0.1 (development)
# ❌ evil.com, ng-support.com (not matching ngnair.com patterns)
        </div>
    </div>

    <!-- Widget Configuration -->
    <div class="example-section">
        <h2>2. Widget Integration with Domain Validation</h2>
        <p>The widget automatically validates origins against trusted patterns:</p>
        
        <div class="code">
&lt;script src="https://auth.ngnair.com/ngnair-auth-widget.js"&gt;&lt;/script&gt;
&lt;script&gt;
// Widget automatically validates origins
const auth = NGnairAuth.init({
    containerId: 'auth-container',
    authUrl: 'https://auth.ngnair.com',
    validateOrigin: true,  // Default: true
    onAuthChange: function(status) {
        console.log('Authenticated status:', status);
    }
});

// Optional: Add custom trusted domains at runtime
NGnairAuth.addTrustedDomain('custom-*.example.com');
&lt;/script&gt;
        </div>
    </div>

    <!-- Live Testing -->
    <div class="example-section">
        <h2>3. Live Domain Validation Test</h2>
        <p>Test different origins against the trusted domain patterns:</p>
        
        <div id="test-container">
            <div class="test-grid">
                <span>ng-support-fe-dev.dev1.ngnair.com</span>
                <button onclick="testDomain('https://ng-support-fe-dev.dev1.ngnair.com')">Test</button>
            </div>
            <div class="test-grid">
                <span>ng-ob.ng-dev.ngnair.com</span>
                <button onclick="testDomain('https://ng-ob.ng-dev.ngnair.com')">Test</button>
            </div>
            <div class="test-grid">
                <span>app.ngnair.com</span>
                <button onclick="testDomain('https://app.ngnair.com')">Test</button>
            </div>
            <div class="test-grid">
                <span>evil.com</span>
                <button onclick="testDomain('https://evil.com')">Test</button>
            </div>
            <div class="test-grid">
                <span>ng-support.com</span>
                <button onclick="testDomain('https://ng-support.com')">Test</button>
            </div>
        </div>
        
        <div id="test-results"></div>
    </div>

    <!-- Custom Domain Testing -->
    <div class="example-section">
        <h2>4. Custom Domain Testing</h2>
        <p>Test any domain against the current trusted patterns:</p>
        
        <div style="margin: 20px 0;">
            <input type="text" id="custom-domain" placeholder="https://example.com" style="width: 300px; padding: 8px;">
            <button onclick="testCustomDomain()">Test Custom Domain</button>
        </div>
        
        <div id="custom-results"></div>
    </div>

    <!-- Security Benefits -->
    <div class="example-section">
        <h2>5. Security Benefits</h2>
        <ul>
            <li><strong>Wildcard Support:</strong> <code>ng-*.com</code> matches all your branded domains</li>
            <li><strong>Automatic Validation:</strong> Messages from untrusted origins are automatically blocked</li>
            <li><strong>Development Friendly:</strong> Localhost patterns for development</li>
            <li><strong>Flexible Configuration:</strong> Environment-based configuration</li>
            <li><strong>Runtime Management:</strong> Add/remove patterns dynamically if needed</li>
        </ul>
    </div>

    <!-- Include the widget for testing -->
    <script src="./ngnair-auth-widget.js"></script>

    <script>
        // Simulate the domain validation logic for testing
        const TRUSTED_PATTERNS = [
            'ng-*-fe-*.dev*.ngnair.com',     // Matches ng-support-fe-dev.dev1.ngnair.com
            'ng-*.ng-*.ngnair.com',          // Matches ng-ob.ng-dev.ngnair.com
            'ng-*.dev*.ngnair.com',          // General pattern for ng-[service].dev[env].ngnair.com
            'ng-*-fe-*.ngnair.com',          // General pattern for ng-[service]-fe-[env].ngnair.com
            '*.ngnair.com',                  // All subdomains of ngnair.com
            'ngnair.com',                    // Exact match
            'localhost',                     // Development
            '127.0.0.1'                      // Local development
        ];

        function isValidDomain(domain, pattern) {
            const regexPattern = pattern
                .replace(/\./g, '\\.')
                .replace(/\*/g, '[^.]*');
            
            const regex = new RegExp(`^${regexPattern}$`, 'i');
            return regex.test(domain);
        }

        function isTrustedDomain(origin) {
            try {
                const url = new URL(origin);
                const domain = url.hostname;
                
                return TRUSTED_PATTERNS.some(pattern => isValidDomain(domain, pattern));
            } catch (e) {
                return false;
            }
        }

        function testDomain(origin) {
            const isTrusted = isTrustedDomain(origin);
            const resultsDiv = document.getElementById('test-results');
            
            const resultClass = isTrusted ? 'trusted' : 'blocked';
            const resultText = isTrusted ? 'TRUSTED ✅' : 'BLOCKED ❌';
            
            resultsDiv.innerHTML = `
                <div class="status ${resultClass}">
                    <strong>${origin}:</strong> ${resultText}
                </div>
            `;
        }

        function testCustomDomain() {
            const input = document.getElementById('custom-domain');
            const origin = input.value.trim();
            
            if (!origin) {
                alert('Please enter a domain to test');
                return;
            }
            
            const isTrusted = isTrustedDomain(origin);
            const resultsDiv = document.getElementById('custom-results');
            
            const resultClass = isTrusted ? 'trusted' : 'blocked';
            const resultText = isTrusted ? 'TRUSTED ✅' : 'BLOCKED ❌';
            
            resultsDiv.innerHTML = `
                <div class="status ${resultClass}">
                    <strong>${origin}:</strong> ${resultText}
                    <br>
                    <small>Tested against patterns: ${TRUSTED_PATTERNS.join(', ')}</small>
                </div>
            `;
        }

        // Show current patterns on load
        window.addEventListener('load', function() {
            console.log('Trusted domain patterns:', TRUSTED_PATTERNS);
        });
    </script>
</body>
</html>
