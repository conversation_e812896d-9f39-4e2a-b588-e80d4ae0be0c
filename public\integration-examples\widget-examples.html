<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NGnair Auth Widget Examples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .example {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .code {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .status {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .auth-container {
            min-height: 400px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <h1>NGnair Authentication Widget Integration Examples</h1>
    
    <p>This page demonstrates different ways to integrate the NGnair authentication system into your website using our JavaScript widget.</p>

    <!-- Example 1: Auto-initialization -->
    <div class="example">
        <h2>Example 1: Auto-initialization with Data Attributes</h2>
        <p>The simplest way - just add data attributes to any div:</p>
        
        <div class="code">
&lt;!-- Include the widget script --&gt;
&lt;script src="https://your-domain.com/ngnair-auth-widget.js"&gt;&lt;/script&gt;

&lt;!-- Add a container with data attributes --&gt;
&lt;div id="auth-auto" 
     data-ngnair-auth
     data-auth-url="http://localhost:3000"
     data-width="100%"
     data-height="500px"
     data-debug="true"&gt;
&lt;/div&gt;
        </div>

        <div id="auth-auto" 
             data-ngnair-auth
             data-auth-url="http://localhost:3000"
             data-width="100%"
             data-height="500px"
             data-debug="true"
             class="auth-container">
        </div>
    </div>

    <!-- Example 2: Manual initialization -->
    <div class="example">
        <h2>Example 2: Manual Initialization with Callbacks</h2>
        <p>More control with JavaScript initialization and event handling:</p>
        
        <div class="code">
&lt;script&gt;
const authWidget = NGnairAuth.init({
    containerId: 'auth-manual',
    authUrl: 'http://localhost:3000',
    width: '100%',
    height: '500px',
    debug: true,
    onAuthChange: function(status, previousStatus) {
        console.log('Auth changed:', status);
        document.getElementById('status-display').textContent = status;
    },
    onReady: function() {
        console.log('Widget is ready!');
    }
});
&lt;/script&gt;
        </div>

        <div class="status">
            <strong>Current Status:</strong> <span id="status-display">Loading...</span>
        </div>

        <div>
            <button onclick="manualWidget.requestAuthStatus()">Check Status</button>
            <button onclick="manualWidget.show()">Show Widget</button>
            <button onclick="manualWidget.hide()">Hide Widget</button>
        </div>

        <div id="auth-manual" class="auth-container"></div>
    </div>

    <!-- Example 3: Event-driven -->
    <div class="example">
        <h2>Example 3: Event-Driven Integration</h2>
        <p>Listen for custom events instead of callbacks:</p>
        
        <div class="code">
&lt;script&gt;
// Listen for authentication events
document.addEventListener('ngnair-authchange', function(event) {
    const { status, previousStatus, timestamp } = event.detail;
    console.log('Auth event:', status);
    
    // Handle different states
    switch(status) {
        case 'login-success':
            // Redirect to dashboard
            window.location.href = '/dashboard';
            break;
        case 'logout':
            // Clear user data
            localStorage.clear();
            break;
    }
});
&lt;/script&gt;
        </div>

        <div id="event-log" class="status">
            <strong>Event Log:</strong><br>
            <div id="event-messages">Waiting for events...</div>
        </div>

        <div id="auth-events" class="auth-container"></div>
    </div>

    <!-- Example 4: Responsive/Modal -->
    <div class="example">
        <h2>Example 4: Modal/Popup Style</h2>
        <p>Show authentication in a modal overlay:</p>
        
        <div class="code">
&lt;style&gt;
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    z-index: 1000;
}
.auth-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    padding: 20px;
}
&lt;/style&gt;
        </div>

        <button onclick="showAuthModal()">Open Authentication Modal</button>

        <div id="auth-modal" class="auth-modal" onclick="hideAuthModal()">
            <div class="auth-modal-content" onclick="event.stopPropagation()">
                <button onclick="hideAuthModal()" style="float: right;">×</button>
                <div id="auth-modal-container" style="width: 400px; height: 500px;"></div>
            </div>
        </div>
    </div>

    <!-- Include the widget script -->
    <script src="./ngnair-auth-widget.js"></script>

    <script>
        // Example 2: Manual initialization
        const manualWidget = NGnairAuth.init({
            containerId: 'auth-manual',
            authUrl: 'http://localhost:3000',
            width: '100%',
            height: '500px',
            debug: true,
            onAuthChange: function(status, previousStatus) {
                console.log('Manual widget auth changed:', status);
                document.getElementById('status-display').textContent = status;
                
                // Show success message
                if (status === 'login-success') {
                    alert('Login successful! You can now access protected features.');
                }
            },
            onReady: function() {
                console.log('Manual widget is ready!');
                document.getElementById('status-display').textContent = 'Ready';
            }
        });

        // Example 3: Event-driven
        const eventWidget = NGnairAuth.init({
            containerId: 'auth-events',
            authUrl: 'http://localhost:3000',
            width: '100%',
            height: '400px',
            debug: true
        });

        // Listen for custom events
        document.addEventListener('ngnair-authchange', function(event) {
            const { status, previousStatus, timestamp } = event.detail;
            const messagesDiv = document.getElementById('event-messages');
            const time = new Date(timestamp).toLocaleTimeString();
            
            messagesDiv.innerHTML += `<br>[${time}] ${previousStatus} → ${status}`;
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        });

        // Example 4: Modal
        let modalWidget = null;

        function showAuthModal() {
            document.getElementById('auth-modal').style.display = 'block';
            
            if (!modalWidget) {
                modalWidget = NGnairAuth.init({
                    containerId: 'auth-modal-container',
                    authUrl: 'http://localhost:3000',
                    width: '100%',
                    height: '100%',
                    showBorder: false,
                    onAuthChange: function(status) {
                        if (status === 'login-success') {
                            hideAuthModal();
                            alert('Welcome! You are now logged in.');
                        }
                    }
                });
            }
        }

        function hideAuthModal() {
            document.getElementById('auth-modal').style.display = 'none';
        }

        // Close modal on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                hideAuthModal();
            }
        });
    </script>

    <style>
        .auth-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 1000;
        }
        .auth-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
    </style>
</body>
</html>
