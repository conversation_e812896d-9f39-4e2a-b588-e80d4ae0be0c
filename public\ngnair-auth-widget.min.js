/**
 * NGnair Authentication Widget v1.0.0
 * Production-ready iframe integration widget
 * 
 * Usage:
 * <script src="https://cdn.ngnair.com/auth-widget/v1/ngnair-auth-widget.min.js"></script>
 * <div id="auth" data-ngnair-auth data-auth-url="https://auth.ngnair.com"></div>
 */
!function(e){"use strict";const t={authUrl:"https://auth.ngnair.com",containerId:"ngnair-auth",width:"100%",height:"600px",onAuthChange:null,onReady:null,onError:null,autoResize:!0,showBorder:!0,borderRadius:"8px",debug:!1};class n{constructor(e={}){this.config={...t,...e},this.iframe=null,this.container=null,this.authStatus="unknown",this.isReady=!1,this.init()}init(){this.log("Initializing NGnair Auth Widget..."),this.container=document.getElementById(this.config.containerId),this.container?(this.createIframe(),this.setupMessageListener(),this.log("Widget initialized successfully")):this.error(`Container with ID '${this.config.containerId}' not found`)}createIframe(){this.iframe=document.createElement("iframe"),this.iframe.src=`${this.config.authUrl}/login`,this.iframe.style.width=this.config.width,this.iframe.style.height=this.config.height,this.iframe.style.border=this.config.showBorder?"1px solid #ddd":"none",this.iframe.style.borderRadius=this.config.borderRadius,this.iframe.title="NGnair Authentication",this.iframe.allow="camera; microphone; geolocation",this.iframe.addEventListener("load",()=>{this.isReady=!0,this.log("Iframe loaded successfully"),setTimeout(()=>{this.requestAuthStatus()},1e3),this.config.onReady&&this.config.onReady()}),this.iframe.addEventListener("error",e=>{this.error("Iframe failed to load",e)}),this.container.appendChild(this.iframe)}setupMessageListener(){e.addEventListener("message",e=>{this.log("Received message:",e.data),"auth-status"===e.data?.type&&this.handleAuthStatusChange(e.data.status)})}handleAuthStatusChange(e){const t=this.authStatus;this.authStatus=e,this.log(`Auth status changed: ${t} → ${e}`),this.config.onAuthChange&&this.config.onAuthChange(e,t),this.dispatchEvent("authchange",{status:e,previousStatus:t,timestamp:Date.now()})}requestAuthStatus(){this.iframe&&this.iframe.contentWindow&&this.isReady?(this.iframe.contentWindow.postMessage({type:"request-auth-status"},"*"),this.log("Requested auth status")):this.log("Cannot request auth status - iframe not ready")}getAuthStatus(){return this.authStatus}isAuthenticated(){return"authenticated"===this.authStatus}show(){this.container&&(this.container.style.display="block")}hide(){this.container&&(this.container.style.display="none")}destroy(){this.iframe&&(this.iframe.remove(),this.iframe=null),this.isReady=!1,this.log("Widget destroyed")}dispatchEvent(e,t){const n=new CustomEvent(`ngnair-${e}`,{detail:t,bubbles:!0});this.container.dispatchEvent(n)}log(...e){this.config.debug&&console.log("[NGnair Auth Widget]",...e)}error(...e){console.error("[NGnair Auth Widget]",...e),this.config.onError&&this.config.onError(...e)}}e.NGnairAuth={init:function(e){return new n(e)},create:function(e){return new n(e)},isSupported:function(){return!!(e.postMessage&&e.addEventListener&&document.createElement)},version:"1.0.0"},document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll("[data-ngnair-auth]").forEach(i=>{const o={containerId:i.id||"ngnair-auth-"+Math.random().toString(36).substr(2,9),authUrl:i.dataset.authUrl||t.authUrl,width:i.dataset.width||t.width,height:i.dataset.height||t.height,debug:"true"===i.dataset.debug};i.id||(i.id=o.containerId),e.NGnairAuth.init(o)})})}(window);
