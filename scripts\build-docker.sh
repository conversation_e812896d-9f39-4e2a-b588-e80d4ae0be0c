#!/bin/bash

# Docker Build Script for Auth Frontend
# Usage: ./scripts/build-docker.sh [dev|prod] [tag]

set -e

# Default values
ENVIRONMENT=${1:-prod}
TAG=${2:-latest}
IMAGE_NAME="auth-frontend"
REGISTRY="docker.io/blee900"

echo "🐳 Building Docker image for $ENVIRONMENT environment..."

if [ "$ENVIRONMENT" = "dev" ] || [ "$ENVIRONMENT" = "development" ]; then
    echo "📦 Building development image..."
    docker build \
        --build-arg NEXT_PUBLIC_APP_ENV=development \
        --build-arg NEXT_PUBLIC_GRAPHQL_URL=http://localhost:3001/graphql \
        --build-arg AUTH_URL=http://localhost:3001/api/auth \
        --build-arg NEXT_PUBLIC_COOKIE_DOMAIN=.ngnair.com \
        --build-arg NEXT_PUBLIC_TRUSTED_DOMAINS="ng-*-fe-*.dev*.ngnair.com,ng-*.ng-*.ngnair.com,ng-*.dev*.ngnair.com,ng-*-fe-*.ngnair.com,*.ngnair.com,ngnair.com,localhost,127.0.0.1" \
        --build-arg NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts-fe-dev.dev1.ngnair.com \
        --build-arg NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth-dev.dev1.ngnair.com \
        --build-arg NEXT_PUBLIC_APP_URL=http://localhost:3000 \
        -t $IMAGE_NAME:dev-$TAG \
        .
    
    echo "✅ Development image built: $IMAGE_NAME:dev-$TAG"
    
    # Tag for registry
    docker tag $IMAGE_NAME:dev-$TAG $REGISTRY/$IMAGE_NAME:dev-$TAG
    echo "🏷️  Tagged for registry: $REGISTRY/$IMAGE_NAME:dev-$TAG"
    
elif [ "$ENVIRONMENT" = "prod" ] || [ "$ENVIRONMENT" = "production" ]; then
    echo "🚀 Building production image..."
    docker build \
        --build-arg NEXT_PUBLIC_APP_ENV=production \
        --build-arg NEXT_PUBLIC_GRAPHQL_URL=https://ng-auth.ngnair.com/graphql \
        --build-arg AUTH_URL=https://ng-auth.ngnair.com/api/auth \
        --build-arg NEXT_PUBLIC_COOKIE_DOMAIN=.ngnair.com \
        --build-arg NEXT_PUBLIC_TRUSTED_DOMAINS="ng-*.ngnair.com,*.ngnair.com,ngnair.com" \
        --build-arg NEXT_PUBLIC_FALLBACK_URL=https://ng-accounts.ngnair.com \
        --build-arg NEXT_PUBLIC_AUTH_SERVICE_URL=https://ng-auth.ngnair.com \
        --build-arg NEXT_PUBLIC_APP_URL=https://ng-auth.ngnair.com \
        -t $IMAGE_NAME:prod-$TAG \
        .
    
    echo "✅ Production image built: $IMAGE_NAME:prod-$TAG"
    
    # Tag for registry
    docker tag $IMAGE_NAME:prod-$TAG $REGISTRY/$IMAGE_NAME:prod-$TAG
    echo "🏷️  Tagged for registry: $REGISTRY/$IMAGE_NAME:prod-$TAG"
    
else
    echo "❌ Invalid environment. Use 'dev' or 'prod'"
    exit 1
fi

echo ""
echo "🎯 Next steps:"
echo "1. Test the image locally:"
if [ "$ENVIRONMENT" = "dev" ] || [ "$ENVIRONMENT" = "development" ]; then
    echo "   docker run -p 3000:3000 $IMAGE_NAME:dev-$TAG"
    echo ""
    echo "2. Push to registry:"
    echo "   docker push $REGISTRY/$IMAGE_NAME:dev-$TAG"
    echo ""
    echo "3. Update deployment to use:"
    echo "   $REGISTRY/$IMAGE_NAME:dev-$TAG"
else
    echo "   docker run -p 3000:3000 $IMAGE_NAME:prod-$TAG"
    echo ""
    echo "2. Push to registry:"
    echo "   docker push $REGISTRY/$IMAGE_NAME:prod-$TAG"
    echo ""
    echo "3. Update deployment to use:"
    echo "   $REGISTRY/$IMAGE_NAME:prod-$TAG"
fi

echo ""
echo "🔍 To verify environment variables in the image:"
echo "   docker run --rm $IMAGE_NAME:$ENVIRONMENT-$TAG env | grep NEXT_PUBLIC_APP_ENV"
