#!/usr/bin/env node

/**
 * Complete Authentication Flow
 * 
 * This script follows the complete authentication flow:
 * 1. Register user (if needed)
 * 2. Verify email 
 * 3. Complete MFA
 * 4. Get real JWT token
 */

const readline = require('readline')

const API_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com/api/v1'
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Walking&Forward37',
  first_name: 'Test',
  last_name: 'User'
}

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve)
  })
}

// Decode JWT payload
function decodeJWTPayload(token) {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT structure')
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString())
    return payload
  } catch (error) {
    console.error('Failed to decode JWT:', error.message)
    return null
  }
}

// Step 1: Check if user exists or register
async function registerUser() {
  console.log('🔐 Step 1: Register User (if needed)')
  console.log('=' .repeat(50))
  
  try {
    console.log(`Registering user: ${TEST_USER.email}`)
    
    const response = await fetch(`${API_BASE_URL}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify({
        email: TEST_USER.email,
        first_name: TEST_USER.first_name,
        last_name: TEST_USER.last_name
      }),
      credentials: 'include'
    })
    
    console.log(`Response Status: ${response.status} ${response.statusText}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Registration successful!')
      console.log('Response data:', JSON.stringify(data, null, 2))
      return data
    } else {
      const errorData = await response.text()
      console.log('⚠️  Registration response:', errorData)
      
      // If user already exists, that's fine - we can proceed
      if (response.status === 422 || errorData.includes('already exists')) {
        console.log('✅ User already exists, proceeding to login...')
        return { message: 'User already exists' }
      } else {
        throw new Error(`Registration failed: ${errorData}`)
      }
    }
    
  } catch (error) {
    console.error('❌ Registration error:', error.message)
    return null
  }
}

// Step 2: Try login to get user_id and verification status
async function attemptLogin() {
  console.log('\n🔐 Step 2: Attempt Login')
  console.log('=' .repeat(50))
  
  try {
    const response = await fetch(`${API_BASE_URL}/login?device_id`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify({
        email: TEST_USER.email,
        password: TEST_USER.password
      }),
      credentials: 'include'
    })
    
    console.log(`Response Status: ${response.status} ${response.statusText}`)
    
    const data = await response.json()
    console.log('Login response:', JSON.stringify(data, null, 2))
    
    return { response, data }
    
  } catch (error) {
    console.error('❌ Login error:', error.message)
    return null
  }
}

// Step 3: Handle email verification
async function handleEmailVerification(userId) {
  console.log('\n📧 Step 3: Email Verification')
  console.log('=' .repeat(50))
  
  console.log(`User ID: ${userId}`)
  console.log('Email verification is required.')
  console.log('\nPlease check your email for a verification link.')
  console.log('The link will look like: https://ng-auth-fe-dev.ngnair.com/verify-email?user_id=...&token=...')
  
  const verificationLink = await askQuestion('\nPaste the verification link here: ')
  
  if (!verificationLink.trim()) {
    console.log('❌ No verification link provided')
    return null
  }
  
  // Extract user_id and token from the link
  try {
    const url = new URL(verificationLink.trim())
    const extractedUserId = url.searchParams.get('user_id')
    const token = url.searchParams.get('token')
    
    if (!extractedUserId || !token) {
      console.log('❌ Could not extract user_id and token from link')
      return null
    }
    
    console.log(`Extracted User ID: ${extractedUserId}`)
    console.log(`Extracted Token: ${token.substring(0, 20)}...`)
    
    // Verify email
    const response = await fetch(`${API_BASE_URL}/verify_email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify({
        user_id: extractedUserId,
        token: token
      }),
      credentials: 'include'
    })
    
    console.log(`Email verification response: ${response.status} ${response.statusText}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Email verified successfully!')
      console.log('Response data:', JSON.stringify(data, null, 2))
      return { userId: extractedUserId, ...data }
    } else {
      const errorData = await response.text()
      console.log('❌ Email verification failed:', errorData)
      return null
    }
    
  } catch (error) {
    console.error('❌ Email verification error:', error.message)
    return null
  }
}

// Step 4: Complete MFA flow
async function completeMFAFlow(userId, mfaToken) {
  console.log('\n📱 Step 4: Complete MFA Flow')
  console.log('=' .repeat(50))
  
  console.log(`User ID: ${userId}`)
  console.log(`MFA Token: ${mfaToken}`)
  
  // Try to send OTP (this might work now that email is verified)
  try {
    console.log('\nTrying to trigger MFA...')
    
    const response = await fetch(`${API_BASE_URL}/send_otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify({
        user_id: userId,
        mfa_token: mfaToken,
        mfa_type: 'sms'
      }),
      credentials: 'include'
    })
    
    console.log(`Send OTP response: ${response.status} ${response.statusText}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ OTP sent successfully!')
      console.log('Response data:', JSON.stringify(data, null, 2))
      
      // Get OTP code from user
      const otpCode = await askQuestion('\nEnter the OTP code you received: ')
      
      if (!otpCode || otpCode.trim().length !== 6) {
        console.log('❌ Invalid OTP code')
        return null
      }
      
      // Verify OTP
      const verifyResponse = await fetch(`${API_BASE_URL}/verify_otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Device-ID': 'test-device-123',
        },
        body: JSON.stringify({
          user_id: userId,
          mfa_token: mfaToken,
          mfa_code: otpCode.trim(),
          mfa_type: 'sms'
        }),
        credentials: 'include'
      })
      
      console.log(`Verify OTP response: ${verifyResponse.status} ${verifyResponse.statusText}`)
      
      if (verifyResponse.ok) {
        const verifyData = await verifyResponse.json()
        console.log('✅ OTP verified successfully!')
        console.log('Response data:', JSON.stringify(verifyData, null, 2))
        return verifyData
      } else {
        const errorData = await verifyResponse.text()
        console.log('❌ OTP verification failed:', errorData)
        return null
      }
      
    } else {
      const errorData = await response.text()
      console.log('❌ Send OTP failed:', errorData)
      return null
    }
    
  } catch (error) {
    console.error('❌ MFA flow error:', error.message)
    return null
  }
}

// Analyze the final JWT token
function analyzeJWT(token) {
  console.log('\n🔍 JWT Token Analysis')
  console.log('=' .repeat(50))
  
  console.log(`Token: ${token}`)
  console.log(`Token length: ${token.length}`)
  
  const payload = decodeJWTPayload(token)
  if (payload) {
    console.log('\n✅ Token decoded successfully:')
    console.log('Token Payload:')
    console.log(JSON.stringify(payload, null, 2))
    
    // Extract user information
    console.log('\n📋 User Information:')
    console.log(`- User ID: ${payload.sub || payload.user_id || 'N/A'}`)
    console.log(`- Email: ${payload.email || 'N/A'}`)
    console.log(`- Device ID: ${payload.device_id || 'N/A'}`)
    
    if (payload.accounts) {
      console.log(`- Accounts: ${payload.accounts.length}`)
      payload.accounts.forEach((acc, i) => {
        console.log(`  ${i + 1}. ${acc.id}: [${acc.scopes ? acc.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    if (payload.partners) {
      console.log(`- Partners: ${payload.partners.length}`)
      payload.partners.forEach((partner, i) => {
        console.log(`  ${i + 1}. ${partner.id}: [${partner.scopes ? partner.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    return payload
  }
  
  return null
}

// Main execution
async function main() {
  try {
    console.log('🚀 Complete Authentication Flow')
    console.log('=' .repeat(50))
    console.log('This script will guide you through the complete authentication process.')
    console.log('You will need access to the email account to verify the email address.')
    
    // Step 1: Register user (if needed)
    const registerResult = await registerUser()
    if (!registerResult) {
      console.log('❌ Registration failed')
      return
    }
    
    // Step 2: Attempt login to get user info
    const loginResult = await attemptLogin()
    if (!loginResult) {
      console.log('❌ Login attempt failed')
      return
    }
    
    const { response, data } = loginResult
    
    // Check if email verification is needed
    if (response.status === 422 && data.error === 'Email not verified') {
      console.log('\n📧 Email verification required')
      
      // We need the user_id - try to extract from error or ask user
      let userId = data.user_id
      if (!userId) {
        userId = await askQuestion('Enter the user ID (from registration or previous attempts): ')
      }
      
      const emailVerifyResult = await handleEmailVerification(userId)
      if (!emailVerifyResult) {
        console.log('❌ Email verification failed')
        return
      }
      
      // Try login again after email verification
      const secondLoginResult = await attemptLogin()
      if (!secondLoginResult) {
        console.log('❌ Second login attempt failed')
        return
      }
      
      const { data: secondData } = secondLoginResult
      
      if (secondData.mfa_required) {
        const mfaResult = await completeMFAFlow(secondData.user_id, secondData.mfa_token)
        if (mfaResult && mfaResult.access_token) {
          analyzeJWT(mfaResult.access_token)
          
          console.log('\n' + '='.repeat(50))
          console.log('🎯 REAL JWT TOKEN OBTAINED!')
          console.log('='.repeat(50))
          console.log(`Access Token: ${mfaResult.access_token}`)
          console.log('\nYou can now use this token in your API requests!')
        }
      }
      
    } else if (data.mfa_required) {
      // Email already verified, proceed with MFA
      const mfaResult = await completeMFAFlow(data.user_id, data.mfa_token)
      if (mfaResult && mfaResult.access_token) {
        analyzeJWT(mfaResult.access_token)
        
        console.log('\n' + '='.repeat(50))
        console.log('🎯 REAL JWT TOKEN OBTAINED!')
        console.log('='.repeat(50))
        console.log(`Access Token: ${mfaResult.access_token}`)
        console.log('\nYou can now use this token in your API requests!')
      }
    } else if (data.access_token) {
      // Direct login success
      analyzeJWT(data.access_token)
      
      console.log('\n' + '='.repeat(50))
      console.log('🎯 REAL JWT TOKEN OBTAINED!')
      console.log('='.repeat(50))
      console.log(`Access Token: ${data.access_token}`)
      console.log('\nYou can now use this token in your API requests!')
    } else {
      console.log('❌ Unexpected login response')
      console.log('Response data:', JSON.stringify(data, null, 2))
    }
    
  } catch (error) {
    console.error('❌ Script error:', error.message)
  } finally {
    rl.close()
  }
}

// Run the script
main().catch(console.error)
