#!/usr/bin/env node

/**
 * Create Mock JWT Token
 * 
 * This script creates a realistic JWT token for testing purposes
 * based on the structure we discovered from your backend
 */

const crypto = require('crypto')

// JWT structure based on your backend response
const JWT_HEADER = {
  "alg": "RS256",
  "typ": "JWT"
}

// Create a realistic payload based on the user information we have
const JWT_PAYLOAD = {
  "sub": "f7b98e6f-95af-4d54-9c53-312ada49ba6e", // User ID from login response
  "email": "<EMAIL>",
  "iat": Math.floor(Date.now() / 1000), // Issued at (now)
  "exp": Math.floor(Date.now() / 1000) + (7776000), // Expires in 90 days (SESSION_TTL_SECONDS)
  "device_id": "test-device-123",
  "accounts": [
    {
      "id": "acc_001",
      "name": "Test Account 1",
      "scopes": [
        "account:view",
        "account:manage",
        "transaction:view",
        "transaction:create"
      ]
    },
    {
      "id": "acc_002", 
      "name": "Test Account 2",
      "scopes": [
        "account:view",
        "transaction:view"
      ]
    }
  ],
  "partners": [
    {
      "id": "partner_001",
      "name": "Test Partner 1",
      "scopes": [
        "partner:view",
        "partner:manage_accounts",
        "partner:view_reports"
      ]
    }
  ],
  "selected_account_id": "acc_001",
  "selected_partner_id": "partner_001",
  "roles": ["user", "account_holder"],
  "permissions": [
    "read",
    "write",
    "account_access",
    "transaction_access"
  ],
  "user_type": "individual",
  "verified": true,
  "mfa_verified": true,
  "session_id": "sess_" + crypto.randomBytes(16).toString('hex')
}

// Create JWT token (mock signature)
function createMockJWT() {
  // Encode header and payload
  const encodedHeader = Buffer.from(JSON.stringify(JWT_HEADER)).toString('base64url')
  const encodedPayload = Buffer.from(JSON.stringify(JWT_PAYLOAD)).toString('base64url')
  
  // Create a mock signature (not cryptographically valid, but realistic looking)
  const mockSignature = crypto
    .createHash('sha256')
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64url')
  
  return `${encodedHeader}.${encodedPayload}.${mockSignature}`
}

// Decode JWT payload for verification
function decodeJWTPayload(token) {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT structure')
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString())
    return payload
  } catch (error) {
    console.error('Failed to decode JWT:', error.message)
    return null
  }
}

// Check if token is expired
function isTokenExpired(token) {
  const payload = decodeJWTPayload(token)
  if (!payload || !payload.exp) {
    return true
  }
  
  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

// Analyze JWT token
function analyzeJWT(token) {
  console.log('🔍 JWT Token Analysis')
  console.log('=' .repeat(50))
  
  console.log(`Token: ${token}`)
  console.log(`Token length: ${token.length}`)
  
  // Decode and analyze token
  const payload = decodeJWTPayload(token)
  if (payload) {
    console.log('\n✅ Token decoded successfully:')
    console.log('Token Payload:')
    console.log(JSON.stringify(payload, null, 2))
    
    // Check expiration
    const expired = isTokenExpired(token)
    console.log(`\nToken Status: ${expired ? '❌ EXPIRED' : '✅ VALID'}`)
    
    if (payload.exp) {
      const expirationDate = new Date(payload.exp * 1000)
      console.log(`Expires at: ${expirationDate.toISOString()}`)
    }
    
    // Extract user information
    console.log('\n📋 User Information:')
    console.log(`- User ID: ${payload.sub || payload.user_id || 'N/A'}`)
    console.log(`- Email: ${payload.email || 'N/A'}`)
    console.log(`- Device ID: ${payload.device_id || 'N/A'}`)
    console.log(`- User Type: ${payload.user_type || 'N/A'}`)
    console.log(`- Verified: ${payload.verified || false}`)
    console.log(`- MFA Verified: ${payload.mfa_verified || false}`)
    console.log(`- Session ID: ${payload.session_id || 'N/A'}`)
    
    if (payload.roles) {
      console.log(`- Roles: [${payload.roles.join(', ')}]`)
    }
    
    if (payload.permissions) {
      console.log(`- Permissions: [${payload.permissions.join(', ')}]`)
    }
    
    if (payload.accounts) {
      console.log(`\n🏦 Accounts (${payload.accounts.length}):`)
      payload.accounts.forEach((acc, i) => {
        const selected = acc.id === payload.selected_account_id ? ' ⭐ SELECTED' : ''
        console.log(`  ${i + 1}. ${acc.name} (${acc.id})${selected}`)
        console.log(`     Scopes: [${acc.scopes ? acc.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    if (payload.partners) {
      console.log(`\n🤝 Partners (${payload.partners.length}):`)
      payload.partners.forEach((partner, i) => {
        const selected = partner.id === payload.selected_partner_id ? ' ⭐ SELECTED' : ''
        console.log(`  ${i + 1}. ${partner.name} (${partner.id})${selected}`)
        console.log(`     Scopes: [${partner.scopes ? partner.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    return payload
  }
  
  return null
}

// Test scope checking functions
function testScopeChecking(token) {
  console.log('\n🔐 Testing Scope Checking')
  console.log('=' .repeat(50))
  
  const payload = decodeJWTPayload(token)
  if (!payload) return
  
  // Test account scopes
  if (payload.accounts) {
    console.log('\n📊 Account Scope Tests:')
    payload.accounts.forEach(account => {
      console.log(`\nAccount: ${account.name} (${account.id})`)
      
      const testScopes = ['account:view', 'account:manage', 'transaction:view', 'transaction:create', 'account:delete']
      testScopes.forEach(scope => {
        const hasScope = account.scopes && account.scopes.includes(scope)
        console.log(`  ${hasScope ? '✅' : '❌'} ${scope}`)
      })
    })
  }
  
  // Test partner scopes
  if (payload.partners) {
    console.log('\n🤝 Partner Scope Tests:')
    payload.partners.forEach(partner => {
      console.log(`\nPartner: ${partner.name} (${partner.id})`)
      
      const testScopes = ['partner:view', 'partner:manage_accounts', 'partner:view_reports', 'partner:admin']
      testScopes.forEach(scope => {
        const hasScope = partner.scopes && partner.scopes.includes(scope)
        console.log(`  ${hasScope ? '✅' : '❌'} ${scope}`)
      })
    })
  }
}

// Main execution
function main() {
  console.log('🎯 Creating Mock JWT Token for Testing')
  console.log('=' .repeat(50))
  
  // Create the mock JWT token
  const token = createMockJWT()
  
  console.log('✅ Mock JWT token created successfully!')
  
  // Analyze the token
  analyzeJWT(token)
  
  // Test scope checking
  testScopeChecking(token)
  
  console.log('\n' + '='.repeat(50))
  console.log('🎯 Mock JWT Token Ready for Testing!')
  console.log('='.repeat(50))
  console.log(`Access Token: ${token}`)
  console.log('\n📝 Usage Instructions:')
  console.log('1. Use this token in your API requests with:')
  console.log(`   Authorization: Bearer ${token}`)
  console.log('\n2. Test with your JWT utilities:')
  console.log('   - JWTUtils.decodePayload(token)')
  console.log('   - JWTUtils.isTokenValid(token)')
  console.log('   - JWTUtils.getUserContext(token)')
  console.log('   - JWTUtils.hasAccountScope("acc_001", "account:view", token)')
  console.log('   - JWTUtils.hasPartnerScope("partner_001", "partner:view", token)')
  console.log('\n3. Update your AUTHSTORE for testing:')
  console.log('   AUTHSTORE.set(token)')
  console.log('\n⚠️  Note: This is a MOCK token for testing purposes only.')
  console.log('   It will not work with real backend verification.')
  console.log('   For production, you need a real JWT from your backend.')
  
  console.log('\n📋 Token Structure Summary:')
  console.log('- Algorithm: RS256 (RSA with SHA-256)')
  console.log('- User ID: f7b98e6f-95af-4d54-9c53-312ada49ba6e')
  console.log('- Email: <EMAIL>')
  console.log('- Accounts: 2 (with different permission levels)')
  console.log('- Partners: 1 (with management permissions)')
  console.log('- Session: 90-day expiration')
  console.log('- Verification: Email and MFA verified')
}

// Run the script
main()
