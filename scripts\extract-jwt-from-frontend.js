#!/usr/bin/env node

/**
 * Extract JWT from Frontend
 * 
 * This script provides instructions for getting the real JWT token
 * from your frontend application
 */

console.log('🎯 How to Get Real JWT Token from Frontend')
console.log('=' .repeat(60))

console.log('\n📋 Step-by-Step Instructions:')
console.log('=' .repeat(60))

console.log('\n1️⃣  Open Your Frontend Application')
console.log('   URL: https://ng-auth-fe-dev.ngnair.com/login')
console.log('   (or http://localhost:3000/login if running locally)')

console.log('\n2️⃣  Open Browser Developer Tools')
console.log('   - Press F12 or right-click → Inspect')
console.log('   - Go to the "Application" tab (Chrome) or "Storage" tab (Firefox)')

console.log('\n3️⃣  Login with Test Credentials')
console.log('   Email: <EMAIL>')
console.log('   Password: Walking&Forward37')

console.log('\n4️⃣  Complete MFA Process')
console.log('   - Enter the SMS code you receive')
console.log('   - Complete the authentication flow')

console.log('\n5️⃣  Extract JWT Token')
console.log('   Option A - From Cookies:')
console.log('   - In Developer Tools → Application → Cookies')
console.log('   - Look for "health-token" cookie')
console.log('   - Copy the value')
console.log('')
console.log('   Option B - From Local Storage:')
console.log('   - In Developer Tools → Application → Local Storage')
console.log('   - Look for "health-token" key')
console.log('   - Copy the value')
console.log('')
console.log('   Option C - From Network Tab:')
console.log('   - Go to Network tab before logging in')
console.log('   - Complete login process')
console.log('   - Look for the login/MFA response')
console.log('   - Find "access_token" in the response')

console.log('\n6️⃣  Verify the Token')
console.log('   - The token should start with "eyJ"')
console.log('   - It should have 3 parts separated by dots')
console.log('   - Length should be several hundred characters')

console.log('\n🔧 Alternative Method - Console Extraction')
console.log('=' .repeat(60))
console.log('If you have access to the frontend code, you can also:')
console.log('')
console.log('1. Open browser console (F12 → Console)')
console.log('2. After successful login, run:')
console.log('   ```javascript')
console.log('   // Get from cookies')
console.log('   document.cookie.split(";").find(c => c.includes("health-token"))')
console.log('')
console.log('   // Get from localStorage')
console.log('   localStorage.getItem("health-token")')
console.log('')
console.log('   // Get from your auth store (if available)')
console.log('   AUTHSTORE.get()')
console.log('   ```')

console.log('\n📱 MFA Information from Backend:')
console.log('=' .repeat(60))
console.log('User ID: f7b98e6f-95af-4d54-9c53-312ada49ba6e')
console.log('MFA Token: f5b65423191734ac3809f1dd4ed7eeb2')
console.log('Phone: *******7890')
console.log('Methods: SMS')

console.log('\n🧪 Testing the Token')
console.log('=' .repeat(60))
console.log('Once you have the JWT token, you can test it with:')
console.log('')
console.log('1. Decode the payload:')
console.log('   node -e "')
console.log('   const token = \'YOUR_TOKEN_HERE\';')
console.log('   const payload = JSON.parse(Buffer.from(token.split(\'.\')[1], \'base64url\').toString());')
console.log('   console.log(JSON.stringify(payload, null, 2));')
console.log('   "')
console.log('')
console.log('2. Test with your JWT utilities:')
console.log('   - JWTUtils.decodePayload(token)')
console.log('   - JWTUtils.isTokenValid(token)')
console.log('   - JWTUtils.getUserContext(token)')
console.log('')
console.log('3. Test API calls:')
console.log('   curl -H "Authorization: Bearer YOUR_TOKEN" \\')
console.log('        https://ng-auth-dev.dev1.ngnair.com/api/v1/user/profile')

console.log('\n🔐 Expected Token Structure')
console.log('=' .repeat(60))
console.log('Your real JWT token should contain:')
console.log('- User ID: f7b98e6f-95af-4d54-9c53-312ada49ba6e')
console.log('- Email: <EMAIL>')
console.log('- Device ID: test-device-123 (or similar)')
console.log('- Accounts array with permissions')
console.log('- Partners array with permissions')
console.log('- Expiration timestamp')
console.log('- Session information')

console.log('\n⚠️  Important Notes')
console.log('=' .repeat(60))
console.log('- The JWT token is cryptographically signed by your backend')
console.log('- It can be verified using the JWKS endpoint')
console.log('- It contains real user permissions and account access')
console.log('- It expires based on your SESSION_TTL_SECONDS (90 days)')
console.log('- It\'s the actual token your application will use')

console.log('\n🎯 What to Do Next')
console.log('=' .repeat(60))
console.log('1. Follow the steps above to get your real JWT token')
console.log('2. Test it with your JWT utilities')
console.log('3. Use it for API testing and development')
console.log('4. Verify it works with your JWKS endpoint')
console.log('5. Test the complete authentication flow in your app')

console.log('\n📞 Need Help?')
console.log('=' .repeat(60))
console.log('If you encounter issues:')
console.log('- Check browser console for errors')
console.log('- Verify network requests in developer tools')
console.log('- Ensure MFA SMS is being received')
console.log('- Check that cookies are being set properly')
console.log('- Verify the frontend is connecting to the correct backend')

console.log('\n✅ Success Indicators')
console.log('=' .repeat(60))
console.log('You\'ll know you have the right token when:')
console.log('- It starts with "eyJ" (JWT header)')
console.log('- It has exactly 3 parts separated by dots')
console.log('- Decoding the payload shows your user information')
console.log('- It includes accounts and partners arrays')
console.log('- API calls with the token return successful responses')

console.log('\n🚀 Ready to Use!')
console.log('=' .repeat(60))
console.log('Once you have the real JWT token, you can:')
console.log('- Use it in all your API requests')
console.log('- Test your JWT utilities with real data')
console.log('- Verify the complete authentication flow')
console.log('- Test scope-based permissions')
console.log('- Validate JWKS integration')

console.log('\nGood luck! 🎉')
