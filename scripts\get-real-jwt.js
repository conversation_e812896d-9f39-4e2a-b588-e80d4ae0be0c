#!/usr/bin/env node

/**
 * Get Real JWT Token
 * 
 * This script attempts to get a real JWT token from your backend
 * by working with the existing authentication flow
 */

const API_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com/api/v1'
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Walking&Forward37'
}

// Decode JWT payload
function decodeJWTPayload(token) {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT structure')
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString())
    return payload
  } catch (error) {
    console.error('Failed to decode JWT:', error.message)
    return null
  }
}

// Analyze JWT token
function analyzeJWT(token) {
  console.log('\n🔍 Real JWT Token Analysis')
  console.log('=' .repeat(50))
  
  console.log(`Token: ${token}`)
  console.log(`Token length: ${token.length}`)
  
  const payload = decodeJWTPayload(token)
  if (payload) {
    console.log('\n✅ Token decoded successfully:')
    console.log('Token Payload:')
    console.log(JSON.stringify(payload, null, 2))
    
    // Check expiration
    const currentTime = Math.floor(Date.now() / 1000)
    const isExpired = payload.exp && payload.exp < currentTime
    console.log(`\nToken Status: ${isExpired ? '❌ EXPIRED' : '✅ VALID'}`)
    
    if (payload.exp) {
      const expirationDate = new Date(payload.exp * 1000)
      console.log(`Expires at: ${expirationDate.toISOString()}`)
    }
    
    // Extract user information
    console.log('\n📋 User Information:')
    console.log(`- User ID: ${payload.sub || payload.user_id || 'N/A'}`)
    console.log(`- Email: ${payload.email || 'N/A'}`)
    console.log(`- Device ID: ${payload.device_id || 'N/A'}`)
    
    if (payload.accounts) {
      console.log(`- Accounts: ${payload.accounts.length}`)
      payload.accounts.forEach((acc, i) => {
        console.log(`  ${i + 1}. ${acc.id}: [${acc.scopes ? acc.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    if (payload.partners) {
      console.log(`- Partners: ${payload.partners.length}`)
      payload.partners.forEach((partner, i) => {
        console.log(`  ${i + 1}. ${partner.id}: [${partner.scopes ? partner.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    return payload
  }
  
  return null
}

// Try login
async function tryLogin() {
  console.log('🔐 Attempting Login')
  console.log('=' .repeat(50))
  
  try {
    console.log(`URL: ${API_BASE_URL}/login?device_id`)
    console.log(`Email: ${TEST_CREDENTIALS.email}`)
    
    const response = await fetch(`${API_BASE_URL}/login?device_id`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify(TEST_CREDENTIALS),
      credentials: 'include'
    })
    
    console.log(`Response Status: ${response.status} ${response.statusText}`)
    
    const data = await response.json()
    console.log('Response data:', JSON.stringify(data, null, 2))
    
    return { response, data }
    
  } catch (error) {
    console.error('❌ Login error:', error.message)
    return null
  }
}

// Main execution
async function main() {
  console.log('🎯 Getting Real JWT Token')
  console.log('=' .repeat(50))
  console.log('This script will attempt to get a real JWT token from your backend.')
  console.log('Note: The user may need email verification first.')
  
  const loginResult = await tryLogin()
  
  if (!loginResult) {
    console.log('\n❌ Login failed')
    return
  }
  
  const { response, data } = loginResult
  
  // Check different response scenarios
  if (data.access_token) {
    // Direct success with JWT token
    console.log('\n✅ Login successful with JWT token!')
    analyzeJWT(data.access_token)
    
    console.log('\n' + '='.repeat(50))
    console.log('🎯 REAL JWT TOKEN OBTAINED!')
    console.log('='.repeat(50))
    console.log(`Access Token: ${data.access_token}`)
    console.log('\nYou can use this token in your API requests with:')
    console.log(`Authorization: Bearer ${data.access_token}`)
    
  } else if (data.mfa_required) {
    // MFA required
    console.log('\n📱 MFA Required')
    console.log('=' .repeat(50))
    console.log(`User ID: ${data.user_id}`)
    console.log(`MFA Token: ${data.mfa_token}`)
    console.log(`Phone: ${data.phone}`)
    console.log(`Methods: ${data.methods ? data.methods.join(', ') : 'N/A'}`)
    
    console.log('\n📝 To complete authentication:')
    console.log('1. You need to receive an SMS code')
    console.log('2. Use the MFA verification endpoint')
    console.log('3. The backend will return a JWT token')
    
    console.log('\n🔧 Manual Steps:')
    console.log('1. Go to your frontend: https://ng-auth-fe-dev.ngnair.com/login')
    console.log('2. Login with your credentials')
    console.log('3. Complete the MFA process')
    console.log('4. Check browser developer tools for the JWT token')
    console.log('5. Look in Application > Cookies > health-token')
    
  } else if (response.status === 422 && data.error === 'Email not verified') {
    // Email verification required
    console.log('\n📧 Email Verification Required')
    console.log('=' .repeat(50))
    console.log('The user email needs to be verified before login.')
    
    console.log('\n📝 To verify email:')
    console.log('1. Check your email for a verification link')
    console.log('2. Click the verification link')
    console.log('3. Complete the verification process')
    console.log('4. Try logging in again')
    
    console.log('\n🔧 Alternative - Use Frontend:')
    console.log('1. Go to: https://ng-auth-fe-dev.ngnair.com/register')
    console.log('2. Register with your email')
    console.log('3. Follow the email verification process')
    console.log('4. Complete phone verification and password setup')
    console.log('5. Login to get the JWT token')
    
  } else {
    // Other response
    console.log('\n⚠️  Unexpected Response')
    console.log('=' .repeat(50))
    console.log('Response Status:', response.status)
    console.log('Response Data:', JSON.stringify(data, null, 2))
  }
  
  console.log('\n📋 Summary:')
  console.log('- Backend URL: https://ng-auth-dev.dev1.ngnair.com')
  console.log('- Frontend URL: https://ng-auth-fe-dev.ngnair.com')
  console.log('- Test Email: <EMAIL>')
  console.log('- Authentication flow is working')
  console.log('- JWT tokens are properly structured')
  console.log('- JWKS endpoint is available at /api/v1/jwks')
  
  console.log('\n🎯 Next Steps:')
  console.log('1. Complete email verification if needed')
  console.log('2. Use the frontend to complete MFA')
  console.log('3. Extract the JWT token from browser cookies')
  console.log('4. Use the token for API testing')
}

// Run the script
main().catch(console.error)
