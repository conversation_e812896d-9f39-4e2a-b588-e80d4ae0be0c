#!/usr/bin/env node

/**
 * Test Backend Connection
 * 
 * This script tests if the backend is accessible
 */

const endpoints = [
  'http://localhost:3000',
  'http://localhost:3001', 
  'http://localhost:3000/api/v1',
  'http://localhost:3001/api/v1',
  'https://ng-auth-dev.dev1.ngnair.com',
  'https://ng-auth-dev.dev1.ngnair.com/api/v1',
]

async function testEndpoint(url) {
  try {
    console.log(`Testing: ${url}`)
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000)
    
    const response = await fetch(url, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'User-Agent': 'JWT-Test-Script/1.0'
      }
    })
    
    clearTimeout(timeoutId)
    
    console.log(`  ✅ Status: ${response.status} ${response.statusText}`)
    
    // Try to get some response data
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      try {
        const data = await response.json()
        console.log(`  📄 JSON Response:`, JSON.stringify(data, null, 2))
      } catch (e) {
        console.log(`  📄 JSON parse error: ${e.message}`)
      }
    } else {
      const text = await response.text()
      console.log(`  📄 Text Response (first 200 chars): ${text.substring(0, 200)}`)
    }
    
    return { url, status: response.status, ok: response.ok }
    
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log(`  ❌ Timeout (5s)`)
    } else {
      console.log(`  ❌ Error: ${error.message}`)
    }
    return { url, error: error.message }
  }
}

async function main() {
  console.log('🔍 Testing Backend Connections')
  console.log('=' .repeat(50))
  
  const results = []
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint)
    results.push(result)
    console.log('')
  }
  
  console.log('📊 Summary:')
  console.log('=' .repeat(50))
  
  const working = results.filter(r => r.ok)
  const errors = results.filter(r => r.error)
  
  if (working.length > 0) {
    console.log('✅ Working endpoints:')
    working.forEach(r => console.log(`  - ${r.url} (${r.status})`))
  }
  
  if (errors.length > 0) {
    console.log('\n❌ Failed endpoints:')
    errors.forEach(r => console.log(`  - ${r.url}: ${r.error}`))
  }
  
  if (working.length === 0) {
    console.log('\n⚠️  No working endpoints found. Make sure your backend is running.')
    console.log('Try starting your Rails server with: rails server -p 3000')
  }
}

main().catch(console.error)
