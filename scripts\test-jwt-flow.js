#!/usr/bin/env node

/**
 * JWT Flow Testing Script
 * 
 * This script demonstrates and tests the complete JWT flow in the application.
 * It can be run independently to verify JWT functionality.
 */

const crypto = require('crypto')

// Mock JWT token creation for testing
function createTestJWT(payload) {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  }
  
  const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url')
  const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url')
  
  // Create a simple signature for testing (not cryptographically secure)
  const signature = crypto
    .createHmac('sha256', 'test-secret')
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64url')
  
  return `${encodedHeader}.${encodedPayload}.${signature}`
}

// Decode JWT payload (for testing purposes)
function decodeJWT(token) {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT structure')
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString())
    return payload
  } catch (error) {
    console.error('Failed to decode JWT:', error.message)
    return null
  }
}

// Check if token is expired
function isTokenExpired(token) {
  const payload = decodeJWT(token)
  if (!payload || !payload.exp) {
    return true
  }
  
  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

// Test scenarios
const testScenarios = [
  {
    name: 'Valid Token',
    payload: {
      sub: 'user123',
      email: '<EMAIL>',
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
      iat: Math.floor(Date.now() / 1000),
      accounts: [
        {
          id: 'acc1',
          scopes: ['account:view', 'account:manage']
        }
      ],
      partners: [
        {
          id: 'partner1',
          scopes: ['partner:view']
        }
      ],
      selected_account_id: 'acc1',
      selected_partner_id: null,
      device_id: 'device-123'
    }
  },
  {
    name: 'Expired Token',
    payload: {
      sub: 'user123',
      email: '<EMAIL>',
      exp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
      iat: Math.floor(Date.now() / 1000) - 7200, // 2 hours ago
      device_id: 'device-123'
    }
  },
  {
    name: 'Token Without Expiration',
    payload: {
      sub: 'user123',
      email: '<EMAIL>',
      iat: Math.floor(Date.now() / 1000),
      device_id: 'device-123'
    }
  }
]

// Run tests
console.log('🔐 JWT Flow Testing Script')
console.log('=' .repeat(50))

testScenarios.forEach((scenario, index) => {
  console.log(`\n${index + 1}. Testing: ${scenario.name}`)
  console.log('-'.repeat(30))
  
  try {
    // Create token
    const token = createTestJWT(scenario.payload)
    console.log(`✓ Token created: ${token.substring(0, 50)}...`)
    
    // Decode token
    const decoded = decodeJWT(token)
    if (decoded) {
      console.log(`✓ Token decoded successfully`)
      console.log(`  - User ID: ${decoded.sub}`)
      console.log(`  - Email: ${decoded.email}`)
      console.log(`  - Issued at: ${new Date(decoded.iat * 1000).toISOString()}`)
      if (decoded.exp) {
        console.log(`  - Expires at: ${new Date(decoded.exp * 1000).toISOString()}`)
      } else {
        console.log(`  - No expiration set`)
      }
      
      if (decoded.accounts) {
        console.log(`  - Accounts: ${decoded.accounts.length}`)
        decoded.accounts.forEach(acc => {
          console.log(`    * ${acc.id}: [${acc.scopes.join(', ')}]`)
        })
      }
      
      if (decoded.partners) {
        console.log(`  - Partners: ${decoded.partners.length}`)
        decoded.partners.forEach(partner => {
          console.log(`    * ${partner.id}: [${partner.scopes.join(', ')}]`)
        })
      }
    } else {
      console.log(`✗ Failed to decode token`)
    }
    
    // Check expiration
    const expired = isTokenExpired(token)
    console.log(`✓ Expiration check: ${expired ? 'EXPIRED' : 'VALID'}`)
    
    // Validate structure
    const parts = token.split('.')
    const validStructure = parts.length === 3
    console.log(`✓ Structure validation: ${validStructure ? 'VALID' : 'INVALID'}`)
    
  } catch (error) {
    console.log(`✗ Test failed: ${error.message}`)
  }
})

// Test malformed tokens
console.log('\n4. Testing: Malformed Tokens')
console.log('-'.repeat(30))

const malformedTokens = [
  'invalid.token',
  'header.invalid-base64.signature',
  '',
  'single-part-token',
  'too.many.parts.in.this.token'
]

malformedTokens.forEach((token, index) => {
  console.log(`\n  ${index + 1}. Testing malformed token: "${token}"`)
  const decoded = decodeJWT(token)
  const expired = isTokenExpired(token)
  console.log(`     Decode result: ${decoded ? 'SUCCESS' : 'FAILED (expected)'}`)
  console.log(`     Expiration: ${expired ? 'EXPIRED (expected)' : 'VALID'}`)
})

// Test JWKS simulation
console.log('\n5. Testing: JWKS Simulation')
console.log('-'.repeat(30))

const mockJWKS = {
  keys: [
    {
      kty: 'RSA',
      use: 'sig',
      kid: 'key1',
      n: 'mock-n-value-for-testing',
      e: 'AQAB'
    },
    {
      kty: 'RSA',
      use: 'sig',
      kid: 'key2',
      n: 'another-mock-n-value',
      e: 'AQAB'
    }
  ]
}

console.log('✓ Mock JWKS created:')
console.log(JSON.stringify(mockJWKS, null, 2))

// Summary
console.log('\n' + '='.repeat(50))
console.log('🎯 Test Summary')
console.log('='.repeat(50))
console.log('✓ JWT token creation and decoding')
console.log('✓ Token expiration validation')
console.log('✓ Token structure validation')
console.log('✓ Malformed token handling')
console.log('✓ JWKS structure simulation')
console.log('\n📝 Next Steps:')
console.log('1. Run: npm test -- --testPathPattern=jwt')
console.log('2. Check JWT integration with: npm run test:coverage')
console.log('3. Test with real backend: npm run dev')

console.log('\n🔗 JWT Flow in Application:')
console.log('1. User logs in → Backend generates JWT')
console.log('2. JWT stored in cookie/localStorage')
console.log('3. JWT sent in Authorization header')
console.log('4. Backend validates JWT using JWKS')
console.log('5. JWT payload used for authorization')
