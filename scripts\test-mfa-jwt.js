#!/usr/bin/env node

/**
 * MFA JWT Testing Script
 * 
 * This script handles the complete MFA flow to get JWT token
 */

const readline = require('readline')

const API_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com/api/v1'
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Walking&Forward37'
}

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Promisify readline question
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve)
  })
}

// Decode JWT payload (without verification for inspection)
function decodeJWTPayload(token) {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT structure')
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString())
    return payload
  } catch (error) {
    console.error('Failed to decode JWT:', error.message)
    return null
  }
}

// Check if token is expired
function isTokenExpired(token) {
  const payload = decodeJWTPayload(token)
  if (!payload || !payload.exp) {
    return true
  }
  
  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

// Step 1: Login and get MFA token
async function loginAndGetMFA() {
  console.log('🔐 Step 1: Login and Get MFA Token')
  console.log('=' .repeat(50))
  
  try {
    console.log(`Making login request to: ${API_BASE_URL}/login?device_id`)
    console.log(`Email: ${TEST_CREDENTIALS.email}`)
    
    const response = await fetch(`${API_BASE_URL}/login?device_id`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify(TEST_CREDENTIALS),
      credentials: 'include'
    })
    
    console.log(`Response Status: ${response.status} ${response.statusText}`)
    
    if (!response.ok) {
      const errorData = await response.text()
      console.error('❌ Login failed:', errorData)
      return null
    }
    
    const data = await response.json()
    console.log('✅ Login successful!')
    console.log('Response data:', JSON.stringify(data, null, 2))
    
    return data
    
  } catch (error) {
    console.error('❌ Network error:', error.message)
    return null
  }
}

// Step 2: Send MFA code
async function sendMFACode(mfaToken, userId) {
  console.log('\n🔐 Step 2: Send MFA Code')
  console.log('=' .repeat(50))
  
  try {
    console.log(`Sending MFA code for user: ${userId}`)
    
    const response = await fetch(`${API_BASE_URL}/send_otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify({
        user_id: userId,
        mfa_token: mfaToken,
        mfa_type: 'sms'
      }),
      credentials: 'include'
    })
    
    console.log(`Response Status: ${response.status} ${response.statusText}`)
    
    if (!response.ok) {
      const errorData = await response.text()
      console.error('❌ Send MFA failed:', errorData)
      return false
    }
    
    const data = await response.json()
    console.log('✅ MFA code sent!')
    console.log('Response data:', JSON.stringify(data, null, 2))
    
    return true
    
  } catch (error) {
    console.error('❌ Network error:', error.message)
    return false
  }
}

// Step 3: Verify MFA and get JWT
async function verifyMFAAndGetJWT(mfaToken, userId, mfaCode) {
  console.log('\n🔐 Step 3: Verify MFA and Get JWT')
  console.log('=' .repeat(50))
  
  try {
    console.log(`Verifying MFA code: ${mfaCode}`)
    
    const response = await fetch(`${API_BASE_URL}/verify_otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify({
        user_id: userId,
        mfa_token: mfaToken,
        mfa_code: mfaCode,
        mfa_type: 'sms'
      }),
      credentials: 'include'
    })
    
    console.log(`Response Status: ${response.status} ${response.statusText}`)
    
    if (!response.ok) {
      const errorData = await response.text()
      console.error('❌ MFA verification failed:', errorData)
      return null
    }
    
    const data = await response.json()
    console.log('✅ MFA verification successful!')
    console.log('Response data:', JSON.stringify(data, null, 2))
    
    return data
    
  } catch (error) {
    console.error('❌ Network error:', error.message)
    return null
  }
}

// Analyze JWT token
function analyzeJWT(token) {
  console.log('\n🔍 JWT Token Analysis')
  console.log('=' .repeat(50))
  
  console.log(`Token (first 50 chars): ${token.substring(0, 50)}...`)
  console.log(`Token length: ${token.length}`)
  
  // Decode and analyze token
  const payload = decodeJWTPayload(token)
  if (payload) {
    console.log('✅ Token decoded successfully:')
    console.log('Token Payload:')
    console.log(JSON.stringify(payload, null, 2))
    
    // Check expiration
    const expired = isTokenExpired(token)
    console.log(`Token Status: ${expired ? '❌ EXPIRED' : '✅ VALID'}`)
    
    if (payload.exp) {
      const expirationDate = new Date(payload.exp * 1000)
      console.log(`Expires at: ${expirationDate.toISOString()}`)
    }
    
    // Extract user information
    console.log('\n📋 User Information:')
    console.log(`- User ID: ${payload.sub || payload.user_id || 'N/A'}`)
    console.log(`- Email: ${payload.email || 'N/A'}`)
    console.log(`- Device ID: ${payload.device_id || 'N/A'}`)
    
    if (payload.accounts) {
      console.log(`- Accounts: ${payload.accounts.length}`)
      payload.accounts.forEach((acc, i) => {
        console.log(`  ${i + 1}. ${acc.id}: [${acc.scopes ? acc.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    if (payload.partners) {
      console.log(`- Partners: ${payload.partners.length}`)
      payload.partners.forEach((partner, i) => {
        console.log(`  ${i + 1}. ${partner.id}: [${partner.scopes ? partner.scopes.join(', ') : 'No scopes'}]`)
      })
    }
    
    if (payload.selected_account_id) {
      console.log(`- Selected Account: ${payload.selected_account_id}`)
    }
    
    if (payload.selected_partner_id) {
      console.log(`- Selected Partner: ${payload.selected_partner_id}`)
    }
    
    return payload
  }
  
  return null
}

// Main execution
async function main() {
  try {
    // Step 1: Login and get MFA requirements
    const loginResponse = await loginAndGetMFA()
    
    if (!loginResponse || !loginResponse.mfa_required) {
      console.log('❌ MFA not required or login failed')
      return
    }
    
    const { user_id, mfa_token, phone } = loginResponse
    
    // Step 2: Send MFA code
    const mfaSent = await sendMFACode(mfa_token, user_id)
    
    if (!mfaSent) {
      console.log('❌ Failed to send MFA code')
      return
    }
    
    console.log(`\n📱 MFA code sent to: ${phone}`)
    
    // Step 3: Get MFA code from user
    const mfaCode = await askQuestion('\nEnter the MFA code you received: ')
    
    if (!mfaCode || mfaCode.trim().length === 0) {
      console.log('❌ No MFA code provided')
      return
    }
    
    // Step 4: Verify MFA and get JWT
    const mfaResponse = await verifyMFAAndGetJWT(mfa_token, user_id, mfaCode.trim())
    
    if (!mfaResponse) {
      console.log('❌ MFA verification failed')
      return
    }
    
    // Extract JWT token
    let token = null
    if (mfaResponse.access_token) {
      token = mfaResponse.access_token
    } else if (mfaResponse.token) {
      token = mfaResponse.token
    } else if (mfaResponse.jwt) {
      token = mfaResponse.jwt
    }
    
    if (token) {
      // Analyze the JWT token
      const payload = analyzeJWT(token)
      
      console.log('\n' + '='.repeat(50))
      console.log('🎯 JWT Token Ready for Use!')
      console.log('='.repeat(50))
      console.log(`Access Token: ${token}`)
      console.log('\nYou can use this token in your API requests with:')
      console.log(`Authorization: Bearer ${token}`)
      
      // Test the token with a simple API call
      console.log('\n🧪 Testing token with API call...')
      try {
        const testResponse = await fetch(`${API_BASE_URL}/user/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          }
        })
        
        console.log(`Test API Status: ${testResponse.status} ${testResponse.statusText}`)
        
        if (testResponse.ok) {
          const userData = await testResponse.json()
          console.log('✅ Token works! User data:')
          console.log(JSON.stringify(userData, null, 2))
        } else {
          const errorData = await testResponse.text()
          console.log('⚠️  Token test failed:', errorData)
        }
      } catch (error) {
        console.log('⚠️  Token test error:', error.message)
      }
      
    } else {
      console.log('❌ No JWT token found in MFA response')
      console.log('Available fields:', Object.keys(mfaResponse))
    }
    
  } catch (error) {
    console.error('❌ Script error:', error.message)
  } finally {
    rl.close()
  }
}

// Run the script
main().catch(console.error)
