#!/usr/bin/env node

/**
 * Real JWT Testing Script
 * 
 * This script tests the actual JWT flow with real credentials
 */

const crypto = require('crypto')

// Configuration from your environment - try multiple possible endpoints
const POSSIBLE_ENDPOINTS = [
  'https://ng-auth-dev.dev1.ngnair.com/api/v1',  // Development server (Rails)
  'https://ng-auth-dev.dev1.ngnair.com',  // Development server root
  'http://localhost:3001/api/v1',  // Alternative port
  'http://localhost:3000/api/v1',  // Next.js proxy
]

const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Walking&Forward37'
}

// JWT verification using the public key from your environment
const JWT_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA77fRvLAd83rgmHKk8jaU
/NQD+K1A+aTvkXFHWcvkqA3Nkae110fUIrI16uJxvqh1T+3BwfT/fYuCQkEeHo+t
aLD9zBHOiCTUZ5ptb1JrjKR2jgCdfdZVC7b8TkZBNBqXcuH91srJASWg6q8GovX1
VgUxwRYIHQAyg63F96zPvm6jWrui7FXfrLaABX8P2/xmaAGkKvo9I9upWkiYRcPG
YTooQv4/XQncpZr8Om3AIdQkCrLNEPYEhlUChFDJUUTe1Zw4ujWpU3FlVhhpjQ2c
/WtxwZPIheztxqNbeD85hIk23D3Cx8nvHa9To5RNBilc9LscSaRVP+K5zyMvYJSY
ywIDAQAB
-----END PUBLIC KEY-----`

// Decode JWT payload (without verification for inspection)
function decodeJWTPayload(token) {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT structure')
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString())
    return payload
  } catch (error) {
    console.error('Failed to decode JWT:', error.message)
    return null
  }
}

// Check if token is expired
function isTokenExpired(token) {
  const payload = decodeJWTPayload(token)
  if (!payload || !payload.exp) {
    return true
  }
  
  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

// Try to find working endpoint
async function findWorkingEndpoint() {
  console.log('🔍 Finding working API endpoint...')

  for (const endpoint of POSSIBLE_ENDPOINTS) {
    try {
      console.log(`Trying: ${endpoint}`)
      const response = await fetch(`${endpoint}/health`, {
        method: 'GET',
        timeout: 5000
      })

      if (response.ok) {
        console.log(`✅ Found working endpoint: ${endpoint}`)
        return endpoint
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - ${error.message}`)
    }
  }

  // If no health endpoint works, try the first one anyway
  console.log(`⚠️  No health endpoint found, using: ${POSSIBLE_ENDPOINTS[0]}`)
  return POSSIBLE_ENDPOINTS[0]
}

// Make login request
async function loginAndGetToken(apiBaseUrl) {
  console.log('🔐 Testing Real JWT Flow')
  console.log('=' .repeat(50))

  try {
    console.log('\n1. Making login request...')
    console.log(`URL: ${apiBaseUrl}/login?device_id`)
    console.log(`Email: ${TEST_CREDENTIALS.email}`)

    const response = await fetch(`${apiBaseUrl}/login?device_id`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Device-ID': 'test-device-123',
      },
      body: JSON.stringify(TEST_CREDENTIALS),
      credentials: 'include'
    })

    console.log(`Response Status: ${response.status} ${response.statusText}`)

    if (!response.ok) {
      const errorData = await response.text()
      console.error('❌ Login failed:', errorData)
      return null
    }

    const data = await response.json()
    console.log('✅ Login successful!')
    console.log('Response data:', JSON.stringify(data, null, 2))

    return data

  } catch (error) {
    console.error('❌ Network error:', error.message)
    return null
  }
}

// Fetch JWKS for verification
async function fetchJWKS(apiBaseUrl) {
  try {
    console.log('\n3. Fetching JWKS for verification...')
    const response = await fetch(`${apiBaseUrl}/jwks`)

    if (!response.ok) {
      throw new Error(`JWKS fetch failed: ${response.status}`)
    }

    const jwks = await response.json()
    console.log('✅ JWKS fetched successfully:')
    console.log(JSON.stringify(jwks, null, 2))

    return jwks

  } catch (error) {
    console.error('❌ JWKS fetch failed:', error.message)
    return null
  }
}

// Fetch user details using the token
async function fetchUserDetails(token, apiBaseUrl) {
  try {
    console.log('\n4. Fetching user details with token...')

    // Try different possible user endpoints
    const userEndpoints = [
      `${apiBaseUrl}/user/me`,
      `${apiBaseUrl}/users/me`,
      `${apiBaseUrl}/auth/me`,
      `${apiBaseUrl}/profile`,
    ]

    for (const endpoint of userEndpoints) {
      try {
        console.log(`Trying user endpoint: ${endpoint}`)
        const response = await fetch(endpoint, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        })

        console.log(`Response: ${response.status} ${response.statusText}`)

        if (response.ok) {
          const userData = await response.json()
          console.log('✅ User details fetched:')
          console.log(JSON.stringify(userData, null, 2))
          return userData
        } else {
          const errorData = await response.text()
          console.log(`Error: ${errorData}`)
        }
      } catch (err) {
        console.log(`Failed: ${err.message}`)
      }
    }

    console.log('❌ No user endpoint worked')
    return null

  } catch (error) {
    console.error('❌ User details fetch failed:', error.message)
    return null
  }
}

// Main execution
async function main() {
  // Step 0: Find working endpoint
  const apiBaseUrl = await findWorkingEndpoint()

  // Step 1: Login and get token
  const loginResponse = await loginAndGetToken(apiBaseUrl)
  
  if (!loginResponse) {
    console.log('\n❌ Failed to get login response')
    return
  }
  
  // Extract token from response
  let token = null
  if (loginResponse.access_token) {
    token = loginResponse.access_token
  } else if (loginResponse.token) {
    token = loginResponse.token
  } else {
    console.log('\n⚠️  No access token found in response')
    console.log('Available fields:', Object.keys(loginResponse))
  }
  
  if (token) {
    console.log('\n2. Analyzing JWT Token...')
    console.log(`Token (first 50 chars): ${token.substring(0, 50)}...`)
    console.log(`Token length: ${token.length}`)
    
    // Decode and analyze token
    const payload = decodeJWTPayload(token)
    if (payload) {
      console.log('✅ Token decoded successfully:')
      console.log('Token Payload:')
      console.log(JSON.stringify(payload, null, 2))
      
      // Check expiration
      const expired = isTokenExpired(token)
      console.log(`Token Status: ${expired ? '❌ EXPIRED' : '✅ VALID'}`)
      
      if (payload.exp) {
        const expirationDate = new Date(payload.exp * 1000)
        console.log(`Expires at: ${expirationDate.toISOString()}`)
      }
      
      // Extract user information
      console.log('\n📋 User Information:')
      console.log(`- User ID: ${payload.sub || payload.user_id || 'N/A'}`)
      console.log(`- Email: ${payload.email || 'N/A'}`)
      console.log(`- Device ID: ${payload.device_id || 'N/A'}`)
      
      if (payload.accounts) {
        console.log(`- Accounts: ${payload.accounts.length}`)
        payload.accounts.forEach((acc, i) => {
          console.log(`  ${i + 1}. ${acc.id}: [${acc.scopes ? acc.scopes.join(', ') : 'No scopes'}]`)
        })
      }
      
      if (payload.partners) {
        console.log(`- Partners: ${payload.partners.length}`)
        payload.partners.forEach((partner, i) => {
          console.log(`  ${i + 1}. ${partner.id}: [${partner.scopes ? partner.scopes.join(', ') : 'No scopes'}]`)
        })
      }
      
      if (payload.selected_account_id) {
        console.log(`- Selected Account: ${payload.selected_account_id}`)
      }
      
      if (payload.selected_partner_id) {
        console.log(`- Selected Partner: ${payload.selected_partner_id}`)
      }
    }
    
    // Step 3: Fetch JWKS
    await fetchJWKS(apiBaseUrl)

    // Step 4: Try to fetch user details
    await fetchUserDetails(token, apiBaseUrl)
    
    console.log('\n' + '='.repeat(50))
    console.log('🎯 JWT Token Ready for Use!')
    console.log('='.repeat(50))
    console.log(`Access Token: ${token}`)
    console.log('\nYou can use this token in your API requests with:')
    console.log(`Authorization: Bearer ${token}`)
    
  } else {
    console.log('\n❌ No token received from login')
  }
}

// Run the script
main().catch(console.error)
