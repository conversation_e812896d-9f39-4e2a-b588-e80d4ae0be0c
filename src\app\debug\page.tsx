'use client';

import { useEffect, useState } from 'react';
import { AUTHSTORE } from '../../lib/auth-storage';
import { authService } from '../../lib/api/auth-service';

export default function DebugPage() {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const info = {
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        NEXT_PUBLIC_APP_ENV: process.env.NEXT_PUBLIC_APP_ENV,
        NEXT_PUBLIC_TRUSTED_DOMAINS: process.env.NEXT_PUBLIC_TRUSTED_DOMAINS,
        NEXT_PUBLIC_COOKIE_DOMAIN: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
        NEXT_PUBLIC_FALLBACK_URL: process.env.NEXT_PUBLIC_FALLBACK_URL,
        NEXT_PUBLIC_AUTH_SERVICE_URL: process.env.NEXT_PUBLIC_AUTH_SERVICE_URL,
      },
      trustedPatterns: AUTHSTORE.getTrustedPatterns(),
      currentUrl: window.location.href,
      urlParams: Object.fromEntries(new URLSearchParams(window.location.search)),
      authStatus: {
        isAuthenticated: AUTHSTORE.isAuthenticated(),
        token: AUTHSTORE.get(),
        isInIframe: AUTHSTORE.isInIframe(),
      },
      jwtPayload: authService.decodeTokenPayload(),
      userContext: authService.getCurrentUserContext(),
      testUrls: [
        'http://localhost:3000/',
        'http://localhost:3001/',
        'http://localhost:3002/',
        'http://localhost:8080/',
        'https://ng-support-fe-dev.dev1.ngnair.com/',
        'https://evil.com/',
      ].map(url => ({
        url,
        isTrusted: AUTHSTORE.isTrustedOrigin(url),
      })),
    };

    setDebugInfo(info);
  }, []);

  const testRedirect = (targetUrl: string) => {
    const authUrl = process.env.NEXT_PUBLIC_AUTH_SERVICE_URL || 'https://ng-auth-fe-dev.dev1.ngnair.com';
    const encodedRedirect = encodeURIComponent(targetUrl);
    const fullUrl = `${authUrl}/login?redirect=${encodedRedirect}`;
    
    console.log('Testing redirect to:', targetUrl);
    console.log('Full auth URL:', fullUrl);
    
    window.open(fullUrl, '_blank');
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">🔍 Authentication Debug Page</h1>
      
      <div className="grid gap-6">
        {/* Environment Variables */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🌍 Environment Variables</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
            {JSON.stringify(debugInfo.environment, null, 2)}
          </pre>
        </div>

        {/* Trusted Patterns */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🔐 Trusted Domain Patterns</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
            {JSON.stringify(debugInfo.trustedPatterns, null, 2)}
          </pre>
        </div>

        {/* Current Page Info */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">📍 Current Page Info</h2>
          <div className="space-y-2">
            <p><strong>URL:</strong> {debugInfo.currentUrl}</p>
            <p><strong>URL Parameters:</strong></p>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
              {JSON.stringify(debugInfo.urlParams, null, 2)}
            </pre>
          </div>
        </div>

        {/* Authentication Status */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🔑 Authentication Status</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
            {JSON.stringify(debugInfo.authStatus, null, 2)}
          </pre>
        </div>

        {/* JWT Payload */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🎫 JWT Token Payload</h2>
          {debugInfo.jwtPayload ? (
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
              {JSON.stringify(debugInfo.jwtPayload, null, 2)}
            </pre>
          ) : (
            <p className="text-gray-500">No JWT token found or token is invalid</p>
          )}
        </div>

        {/* User Context */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">👤 User Context</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg">User ID:</h3>
              <p className="text-gray-700">{debugInfo.userContext?.userId || 'Not available'}</p>
            </div>

            <div>
              <h3 className="font-semibold text-lg">Accounts:</h3>
              {debugInfo.userContext?.accounts?.length > 0 ? (
                <div className="space-y-2">
                  {debugInfo.userContext.accounts.map((account: any, index: number) => (
                    <div key={index} className={`p-3 rounded ${account.id === debugInfo.userContext?.selectedAccountId ? 'bg-green-100 border-green-300' : 'bg-gray-100'}`}>
                      <p><strong>ID:</strong> {account.id} {account.id === debugInfo.userContext?.selectedAccountId && '(Selected)'}</p>
                      <p><strong>Role:</strong> {account.role}</p>
                      <p><strong>Scopes:</strong> {account.scopes.join(', ')}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No accounts available</p>
              )}
            </div>

            <div>
              <h3 className="font-semibold text-lg">Partners:</h3>
              {debugInfo.userContext?.partners?.length > 0 ? (
                <div className="space-y-2">
                  {debugInfo.userContext.partners.map((partner: any, index: number) => (
                    <div key={index} className={`p-3 rounded ${partner.id === debugInfo.userContext?.selectedPartnerId ? 'bg-blue-100 border-blue-300' : 'bg-gray-100'}`}>
                      <p><strong>ID:</strong> {partner.id} {partner.id === debugInfo.userContext?.selectedPartnerId && '(Selected)'}</p>
                      <p><strong>Role:</strong> {partner.role}</p>
                      <p><strong>Scopes:</strong> {partner.scopes.join(', ')}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No partners available</p>
              )}
            </div>
          </div>
        </div>

        {/* URL Trust Test */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🧪 URL Trust Test</h2>
          <div className="space-y-2">
            {debugInfo.testUrls?.map((test: any, index: number) => (
              <div key={index} className={`p-3 rounded ${test.isTrusted ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <span className="font-mono text-sm">{test.url}</span>
                <span className="ml-4 font-semibold">
                  {test.isTrusted ? '✅ Trusted' : '❌ Not Trusted'}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Test Buttons */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🚀 Test Authentication Redirects</h2>
          <div className="space-y-2">
            <button 
              onClick={() => testRedirect('http://localhost:3000/')}
              className="bg-blue-500 text-white px-4 py-2 rounded mr-2 hover:bg-blue-600"
            >
              Test localhost:3000
            </button>
            <button 
              onClick={() => testRedirect('http://localhost:3001/')}
              className="bg-blue-500 text-white px-4 py-2 rounded mr-2 hover:bg-blue-600"
            >
              Test localhost:3001
            </button>
            <button 
              onClick={() => testRedirect('http://localhost:3002/')}
              className="bg-blue-500 text-white px-4 py-2 rounded mr-2 hover:bg-blue-600"
            >
              Test localhost:3002
            </button>
            <button 
              onClick={() => testRedirect('http://localhost:3000/dashboard?welcome=true')}
              className="bg-green-500 text-white px-4 py-2 rounded mr-2 hover:bg-green-600"
            >
              Test with params
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
          <h2 className="text-xl font-semibold mb-4">📝 How to Test</h2>
          <ol className="list-decimal list-inside space-y-2">
            <li>Click one of the test buttons above to open the auth page</li>
            <li>Login with: <code className="bg-gray-200 px-2 py-1 rounded"><EMAIL></code> / <code className="bg-gray-200 px-2 py-1 rounded">Walking&Forward37</code></li>
            <li>After successful login, you should be redirected back to the target URL</li>
            <li>Check the browser console for any error messages</li>
            <li>Verify that authentication tokens are stored properly</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
