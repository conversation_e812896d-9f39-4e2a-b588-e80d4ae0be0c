'use client';

import { useState, useEffect } from 'react';

export default function IframeDemoPage() {
  const [authStatus, setAuthStatus] = useState<string>('unknown');
  const [messages, setMessages] = useState<string[]>([]);

  useEffect(() => {
    // Listen for messages from the iframe
    const handleMessage = (event: MessageEvent) => {
      // In production, verify event.origin for security
      if (event.data?.type === 'auth-status') {
        setAuthStatus(event.data.status);
        setMessages(prev => [...prev, `Received: ${event.data.status} at ${new Date().toLocaleTimeString()}`]);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const requestAuthStatus = () => {
    const iframe = document.getElementById('auth-iframe') as HTMLIFrameElement;
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage({ type: 'request-auth-status' }, '*');
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-6">Iframe Authentication Demo</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Parent Window Controls */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Parent Window</h2>
          
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Auth Status:</strong> {authStatus}</p>
          </div>
          
          <button 
            onClick={requestAuthStatus}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Request Auth Status from Iframe
          </button>
          
          <div className="bg-gray-50 p-4 rounded max-h-40 overflow-y-auto">
            <h3 className="font-semibold mb-2">Messages:</h3>
            {messages.length === 0 ? (
              <p className="text-gray-500">No messages yet...</p>
            ) : (
              <ul className="space-y-1">
                {messages.map((msg, index) => (
                  <li key={index} className="text-sm">{msg}</li>
                ))}
              </ul>
            )}
          </div>
        </div>

        {/* Iframe */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Authentication Iframe</h2>
          
          <div className="border rounded">
            <iframe
              id="auth-iframe"
              src="/login"
              className="w-full h-96 border-0"
              title="Authentication"
            />
          </div>
          
          <p className="text-sm text-gray-600">
            The iframe above contains your authentication system. 
            It will automatically communicate authentication status changes to this parent window.
          </p>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-8 bg-blue-50 p-6 rounded">
        <h3 className="text-lg font-semibold mb-3">How it works:</h3>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>The iframe loads your authentication system with js-cookie integration</li>
          <li>When users log in/out, the iframe automatically notifies this parent window</li>
          <li>Authentication tokens are stored in cookies with proper iframe-compatible settings</li>
          <li>The parent window can request the current authentication status at any time</li>
          <li>Both localStorage and cookies are used for maximum compatibility</li>
        </ol>
      </div>

      {/* Integration Code Example */}
      <div className="mt-8 bg-gray-900 text-green-400 p-6 rounded">
        <h3 className="text-lg font-semibold mb-3 text-white">Integration Code Example:</h3>
        <pre className="text-sm overflow-x-auto">
{`// Listen for authentication status changes
window.addEventListener('message', (event) => {
  if (event.data?.type === 'auth-status') {
    console.log('Auth status:', event.data.status);
    // Handle authentication status change
    switch(event.data.status) {
      case 'authenticated':
        // User is logged in
        break;
      case 'unauthenticated':
        // User is not logged in
        break;
      case 'login-success':
        // User just logged in
        break;
      case 'logout':
        // User just logged out
        break;
    }
  }
});

// Request current auth status
iframe.contentWindow.postMessage({ 
  type: 'request-auth-status' 
}, '*');`}
        </pre>
      </div>
    </div>
  );
}
