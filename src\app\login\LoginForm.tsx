// src/app/login/LoginForm.tsx
'use client';

import { useState } from 'react';
import { Button } from '../../components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Checkbox } from '../../components/ui/checkbox';
import { LockIcon, MailIcon } from 'lucide-react';
import { authService, ApiError } from '../../lib/api/auth-service';
import { AUTHSTORE } from '../../lib/auth-storage';

// Utility function to validate redirect URLs against trusted domains
const isValidRedirectUrl = (redirectUrl: string): boolean => {
  try {
    const url = new URL(redirectUrl);
    return AUTHSTORE.isTrustedOrigin(url.origin);
  } catch (e) {
    console.warn('Invalid redirect URL:', redirectUrl);
    return false;
  }
};

interface LoginFormProps {
  onSuccess?: (mfaRequired: boolean, userId?: string, mfaToken?: string, phoneNumber?: string) => void;
  showRegisterLink?: boolean;
  className?: string;
}

export default function LoginForm({ onSuccess, showRegisterLink = false, className = "" }: LoginFormProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!email || !password) {
      setError('Please fill in all fields.');
      return;
    }

    setLoading(true);

    try {
      const response = await authService.login({
        email: email.trim(),
        password,
      });

      // Debug logging (only in development)
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Login response:', response);
      }

      // Check if login was successful (success: "True" or success: true)
      const isSuccess = response.success === "True" || response.success === true;
      const requiresMfa = response.mfa_required === true;

      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('isSuccess:', isSuccess);
        console.log('requiresMfa:', requiresMfa);
        console.log('response.success:', response.success);
        console.log('response.mfa_required:', response.mfa_required);
      }

      // Handle successful login response
      if (isSuccess && response.device_id) {
        // Store device_id for session management (Rails backend sets httpOnly cookies)
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('device_id', response.device_id);
        }

        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Login successful - Device ID:', response.device_id);
          console.log('Rails backend should have set httpOnly cookies');
        }

        // If there's an access token, store it
        if (response.access_token) {
          AUTHSTORE.set(response.access_token);
          if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
            console.log('Access token stored');
          }
        }
      } else if (response.access_token && !requiresMfa) {
        // Fallback for access_token response without device_id
        AUTHSTORE.set(response.access_token);
      } else if (response.token && !requiresMfa) {
        // Fallback for older token format
        AUTHSTORE.set(response.token);
      }

      // Handle MFA or redirect logic
      if (requiresMfa && response.user_id && response.mfa_token) {
        // MFA is required, call onSuccess to show MFA screen
        if (onSuccess) {
          onSuccess(requiresMfa, response.user_id, response.mfa_token, response.phone_number);
        }
      } else {
        // No MFA required, redirect to original microservice or fallback
        const searchParams = new URLSearchParams(window.location.search);
        const redirectTo = searchParams.get('redirect');

        if (redirectTo) {
          // Validate redirect URL for security
          if (isValidRedirectUrl(redirectTo)) {
            // Redirect back to the original microservice
            if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
              console.log('Redirecting back to microservice:', redirectTo);
            }
            window.location.href = redirectTo;
          } else {
            // Invalid redirect URL, log warning and use fallback
            console.warn('Invalid or untrusted redirect URL:', redirectTo);
            const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-accounts-fe-dev.dev1.ngnair.com';
            if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
              console.log('Using fallback due to invalid redirect:', fallbackUrl);
            }
            window.location.href = fallbackUrl;
          }
        } else {
          // Fallback to accounts frontend using environment variable
          const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-accounts-fe-dev.dev1.ngnair.com';
          if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
            console.log('No redirect specified, using fallback:', fallbackUrl);
          }
          window.location.href = fallbackUrl;
        }
      }
    } catch (err) {
      console.error('Login error:', err);
      const apiError = err as ApiError;
      setError(apiError.message || 'An error occurred during login.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8 ${className}`}>
      <Card className="w-full max-w-md space-y-8">
        <CardHeader className="text-center">
          <div className="mx-auto h-16 w-auto">
            <img
              className="mx-auto h-16 w-auto"
              src="/images/logo.png"
              alt="Company Logo"
            />
          </div>
          <CardTitle className="mt-6 text-left text-3xl font-extrabold text-gray-900 dark:text-gray-100">
            Welcome back
          </CardTitle>
          {showRegisterLink && (
            <p className="text-left text-sm text-gray-600 dark:text-gray-400 mt-2">
              Don&apos;t have an account?{' '}
              <a href="/register" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                Sign up
              </a>
            </p>
          )}
        </CardHeader>
        <CardContent>
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4 border border-red-200 dark:border-red-800">
                <div className="text-sm text-red-700 dark:text-red-400">{error}</div>
              </div>
            )}

            <div>
              <Label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email address
              </Label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MailIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                </div>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="pl-10"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Password
              </Label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  className="pl-10"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Checkbox
                  id="remember-me"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <Label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                  Remember me
                </Label>
              </div>

              <div className="text-sm">
                <a
                  href="/forgot-password"
                  className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Forgot your password?
                </a>
              </div>
            </div>

            <div>
              <Button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 dark:bg-blue-700 dark:hover:bg-blue-800 dark:focus:ring-blue-600"
              >
                {loading ? 'Signing in...' : 'Sign in'}
              </Button>
            </div>
          </form>
        </CardContent>
        {/* Demo Credentials - Only show in development */}
        {process.env.NEXT_PUBLIC_APP_ENV === 'development' && (
          <CardFooter className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
            <div className="w-full text-center">
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 font-medium">Demo Credentials</p>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> Walking&Forward37</p>
              </div>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}
