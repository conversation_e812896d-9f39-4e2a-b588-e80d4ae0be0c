// login/page.tsx
'use client';

import { useState, useEffect } from 'react';
import LoginForm from './LoginForm';
import MfaVerification from '../../components/MfaVerification';
import { AUTHSTORE } from '../../lib/auth-storage';
import { authService } from '../../lib/api/auth-service';

// Utility function to validate redirect URLs against trusted domains
const isValidRedirectUrl = (redirectUrl: string): boolean => {
  try {
    const url = new URL(redirectUrl);
    return AUTHSTORE.isTrustedOrigin(url.origin);
  } catch (e) {
    console.warn('Invalid redirect URL:', redirectUrl);
    return false;
  }
};

export default function LoginPage() {
  const [showMfa, setShowMfa] = useState(false);
  const [userId, setUserId] = useState<string>('');
  const [mfaToken, setMfaToken] = useState<string>('');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Check if user is already authenticated and redirect if needed
  useEffect(() => {
    const checkExistingAuth = async () => {
      try {
        // Check if we have a token
        const existingToken = AUTHSTORE.get();

        if (existingToken) {
          if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
            console.log('Found existing token, checking validity...');
          }

          // Validate token using local JWT validation (no API calls)
          const isTokenValid = authService.validateToken();

          if (isTokenValid) {
            // Token is valid, proceed with redirect
            const searchParams = new URLSearchParams(window.location.search);
            const redirectTo = searchParams.get('redirect');

            if (redirectTo && isValidRedirectUrl(redirectTo)) {
              if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
                console.log('Valid token found, redirecting to:', redirectTo);
              }
              window.location.href = redirectTo;
              return; // Don't set isCheckingAuth to false, let the redirect happen
            } else if (redirectTo) {
              // Invalid redirect URL, log warning and use fallback
              console.warn('Invalid or untrusted redirect URL:', redirectTo);
              const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-accounts-fe-dev.dev1.ngnair.com';
              if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
                console.log('Using fallback due to invalid redirect:', fallbackUrl);
              }
              window.location.href = fallbackUrl;
              return;
            } else {
              // No redirect parameter, use fallback
              const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-accounts-fe-dev.dev1.ngnair.com';
              if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
                console.log('Valid token found, no redirect specified, using fallback:', fallbackUrl);
              }
              window.location.href = fallbackUrl;
              return;
            }
          } else {
            // Token validation failed, clear it and show login form
            if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
              console.log('Token validation failed, clearing token');
            }
            AUTHSTORE.clear();
          }
        }
      } catch (error) {
        console.error('Error checking existing auth:', error);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkExistingAuth();
  }, []);

  const handleLoginSuccess = (mfaRequired?: boolean, userId?: string, mfaToken?: string, phoneNumber?: string) => {
    if (mfaRequired === true && userId && mfaToken) {
      setUserId(userId);
      setMfaToken(mfaToken);
      setPhoneNumber(phoneNumber || '');
      setShowMfa(true);
    } else {
      // No MFA required, redirect to original microservice or fallback
      const searchParams = new URLSearchParams(window.location.search);
      const redirectTo = searchParams.get('redirect');

      if (redirectTo) {
        // Validate redirect URL for security
        if (isValidRedirectUrl(redirectTo)) {
          // Redirect back to the original microservice
          console.log('Redirecting back to microservice:', redirectTo);
          window.location.href = redirectTo;
        } else {
          // Invalid redirect URL, log warning and use fallback
          console.warn('Invalid or untrusted redirect URL:', redirectTo);
          const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-accounts-fe-dev.dev1.ngnair.com';
          console.log('Using fallback due to invalid redirect:', fallbackUrl);
          window.location.href = fallbackUrl;
        }
      } else {
        // Fallback to accounts frontend using environment variable
        const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-accounts-fe-dev.dev1.ngnair.com';
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('No redirect specified, using fallback:', fallbackUrl);
        }
        window.location.href = fallbackUrl;
      }
    }
  };

  const handleMfaSuccess = () => {
    // After MFA success, redirect to original microservice or fallback
    const searchParams = new URLSearchParams(window.location.search);
    const redirectTo = searchParams.get('redirect');

    if (redirectTo) {
      // Validate redirect URL for security
      if (isValidRedirectUrl(redirectTo)) {
        // Redirect back to the original microservice
        console.log('MFA complete, redirecting back to microservice:', redirectTo);
        window.location.href = redirectTo;
      } else {
        // Invalid redirect URL, log warning and use fallback
        console.warn('Invalid or untrusted redirect URL after MFA:', redirectTo);
        const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-accounts-fe-dev.dev1.ngnair.com';
        console.log('Using fallback due to invalid redirect after MFA:', fallbackUrl);
        window.location.href = fallbackUrl;
      }
    } else {
      // Fallback to accounts frontend using environment variable
      const fallbackUrl = process.env.NEXT_PUBLIC_FALLBACK_URL || 'https://ng-accounts-fe-dev.dev1.ngnair.com';
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('MFA complete, no redirect specified, using fallback:', fallbackUrl);
      }
      window.location.href = fallbackUrl;
    }
  };

  const handleBackToLogin = () => {
    setShowMfa(false);
    setUserId('');
    setMfaToken('');
  };

  const handleMfaTimeout = () => {
    // Redirect back to login when timer expires
    setShowMfa(false);
    setUserId('');
    setMfaToken('');
  };

  // Show loading state while checking authentication
  if (isCheckingAuth) {
    return (
      <main className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </main>
    );
  }

  return (
    <main>
      {showMfa ? (
        <MfaVerification
          userId={userId}
          mfaToken={mfaToken}
          phoneNumber={phoneNumber}
          onSuccess={handleMfaSuccess}
          onBack={handleBackToLogin}
          onTimeout={handleMfaTimeout}
        />
      ) : (
        <LoginForm onSuccess={handleLoginSuccess} showRegisterLink={true} />
      )}
    </main>
  );
}