'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MfaVerification from '@/components/MfaVerification';

export default function MfaDemoPage() {
  const [showMfa, setShowMfa] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('+1234567890');
  const [environment, setEnvironment] = useState(process.env.NEXT_PUBLIC_APP_ENV || 'production');

  const handleShowMfa = () => {
    setShowMfa(true);
  };

  const handleBackToDemo = () => {
    setShowMfa(false);
  };

  const maskPhoneNumber = (phone: string): string => {
    if (!phone) return '';
    
    // In development/staging, show full number
    if (environment === 'development') {
      return phone;
    }
    
    // In production, mask all but last 4 digits
    if (phone.length <= 4) {
      return phone; // If phone is 4 digits or less, show as is
    }
    
    const lastFour = phone.slice(-4);
    const maskedPart = '*'.repeat(phone.length - 4);
    return maskedPart + lastFour;
  };

  if (showMfa) {
    return (
      <MfaVerification
        userId="demo-user-123"
        mfaToken="demo-mfa-token"
        phoneNumber={phoneNumber}
        onSuccess={() => alert('MFA Success!')}
        onBack={handleBackToDemo}
        onTimeout={() => alert('MFA Timeout!')}
      />
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center text-2xl font-bold">
            MFA Phone Number Masking Demo
          </CardTitle>
          <p className="text-center text-sm text-gray-600">
            Test how phone numbers are displayed in different environments
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="text"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="+1234567890"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="environment">Environment Mode</Label>
            <select
              id="environment"
              value={environment}
              onChange={(e) => setEnvironment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="development">Development</option>
              <option value="production">Production</option>
            </select>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Phone Number Display Preview:</h3>
            <div className="space-y-2">
              <div>
                <span className="text-sm text-gray-600">Original: </span>
                <span className="font-mono text-sm">{phoneNumber}</span>
              </div>
              <div>
                <span className="text-sm text-gray-600">Displayed ({environment}): </span>
                <span className="font-mono text-sm font-medium">{maskPhoneNumber(phoneNumber)}</span>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">Environment Behavior:</h3>
            <div className="space-y-1 text-sm text-blue-800">
              <div>
                <strong>Development:</strong> Shows full phone number
              </div>
              <div>
                <strong>Production:</strong> Masks all but last 4 digits
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-medium text-yellow-900 mb-2">Current Environment:</h3>
            <div className="text-sm text-yellow-800">
              <strong>NEXT_PUBLIC_APP_ENV:</strong> {process.env.NEXT_PUBLIC_APP_ENV || 'not set'}
            </div>
          </div>

          <Button 
            onClick={handleShowMfa}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            Test MFA Component
          </Button>

          <div className="text-center">
            <Button 
              variant="outline"
              onClick={() => window.location.href = '/login'}
              className="text-sm"
            >
              Back to Login
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
