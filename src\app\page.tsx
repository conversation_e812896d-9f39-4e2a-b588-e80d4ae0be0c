'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function HomePage() {
  const navigateTo = (path: string) => {
    window.location.href = path;
  };

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <Card className="w-full max-w-md border shadow-xl">
        <CardHeader className="space-y-1">
          <CardTitle className="text-center text-2xl font-bold">
            <img src="/images/logo.png" className="mx-auto h-16" alt="NGnair Logo" />
          </CardTitle>
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              NGnair Payments
            </h2>
            <p className="text-sm text-gray-600">
              Authentication Demo
            </p>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => navigateTo('/login')}
          >
            Sign In
          </Button>
          
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigateTo('/register')}
          >
            Create Account
          </Button>
          
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigateTo('/forgot-password')}
          >
            Forgot Password
          </Button>
          
          <div className="pt-4 border-t">
            <p className="text-xs text-gray-500 text-center mb-2">Demo Reset Flow:</p>
            <Button
              variant="link"
              className="w-full text-xs text-blue-600 hover:text-blue-500"
              onClick={() => navigateTo('/reset-password?token=demo-token')}
            >
              Test Reset Password Flow
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
