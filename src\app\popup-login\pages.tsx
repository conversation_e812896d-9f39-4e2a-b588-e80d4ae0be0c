// popup-login/page.tsx
'use client';

import { useSearchParams } from 'next/navigation';
import LoginForm from '../login/LoginForm';

export default function PopupLoginPage() {
  const searchParams = useSearchParams();
  const redirectUri = searchParams.get('redirect_uri');

  const handleLoginSuccess = (mfaRequired: boolean) => {
    if (!redirectUri) return;
    if (mfaRequired) {
      window.location.href = `/mfa?redirect_uri=${encodeURIComponent(redirectUri)}`;
    } else {
      window.opener?.postMessage({ type: 'AUTH_SUCCESS' }, redirectUri);
      window.close();
    }
  };

  return (
    <main className="popup-login-page">
      <h1>Sign in to NGnair</h1>
      {!redirectUri && <p className="error">Missing redirect_uri</p>}
      {redirectUri && <LoginForm onSuccess={handleLoginSuccess} />}
    </main>
  );
}