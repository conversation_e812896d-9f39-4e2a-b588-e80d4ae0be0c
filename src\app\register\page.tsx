/* eslint-disable @next/next/no-img-element */
'use client';

import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { BriefcaseBusiness, MailIcon, UserIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { authService, parseVerificationLink, ApiError } from '@/lib/api/auth-service';

const Page1 = (props: {
  form: any;
  setForm: any;
  onContinue: () => void;
  error?: string;
  submitting?: boolean;
  preEmail?: boolean;
}) => {
  const [agreeToTermAndService, setAgreeToTermAndService] = useState(false);
  const redirectToLogin = () => {
    // Get the current URL's query parameters
    const queryParams = new URLSearchParams(window.location.search);

    // Construct the new URL for the login page
    const loginUrl = new URL('/login', window.location.origin);

    // Append each query parameter to the new URL
    queryParams.forEach((value, key) => {
      loginUrl.searchParams.append(key, value);
    });

    // Redirect to the new URL
    window.location.href = loginUrl.toString();
  };

  return (
    <>
      <CardHeader className="space-y-1">
        <div className="text-center">
          <img src="/images/logo.png" className="mx-auto h-16" alt="NGnair Logo" />
        </div>
        <div className="text-left">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Create your account
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Register for an account to get started
          </p>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <div className="space-y-2">
            <Label htmlFor="name">First Name</Label>
            <div className="relative">
              <UserIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="name"
                placeholder="John"
                type="text"
                className="pl-10"
                value={props.form.firstName}
                onChange={(e) => props.setForm({ ...props.form, firstName: e.target.value })}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="last-name">Last Name</Label>
            <div className="relative">
              <UserIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="last-name"
                placeholder="Doe"
                type="text"
                className="pl-10"
                value={props.form.lastName}
                onChange={(e) => props.setForm({ ...props.form, lastName: e.target.value })}
              />
            </div>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <div className="relative">
            <MailIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              disabled={props.preEmail}
              id="email"
              placeholder="<EMAIL>"
              type="email"
              className="pl-10"
              value={props.form.email}
              onChange={(e) => props.setForm({ ...props.form, email: e.target.value })}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="title">Title</Label>
          <div className="relative">
            <BriefcaseBusiness className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="title"
              placeholder="CEO, Owner, Developer"
              type="text"
              className="pl-10"
              value={props.form.title}
              onChange={(e) => props.setForm({ ...props.form, title: e.target.value })}
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="terms"
            checked={agreeToTermAndService}
            onChange={(e) => setAgreeToTermAndService(e.target.checked)}
          />
          <Label htmlFor="terms" className="text-sm">
            I agree to the{' '}
            <a href="/terms-and-services" className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
              Terms of Service
            </a>{' '}
            of NGnair Payments
          </Label>
        </div>
        <Button
          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          onClick={() => {
            if (agreeToTermAndService) {
              props.onContinue();
            }
          }}
          disabled={props.submitting || !agreeToTermAndService}
        >
          {props.submitting ? 'Sending Verification Email...' : 'Send Verification Email'}
        </Button>
        {props.error && (
          <p className="mx-auto -mt-2 text-center text-xs text-red-600 dark:text-red-400">{props.error}</p>
        )}
      </CardContent>
      <CardFooter>
        <div className="w-full text-left text-sm text-gray-600 dark:text-gray-400">
          Already have an account?{' '}
          <button
            onClick={redirectToLogin}
            className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 h-auto p-0 font-normal"
          >
            Sign in
          </button>
        </div>
      </CardFooter>
    </>
  );
};

export default function RegisterPage() {
  const [form, setForm] = useState({
    email: '',
    firstName: '',
    lastName: '',
    title: '',
  });
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [preEmail, setPreEmail] = useState(false);

  const preloadEmail = async () => {
    // check query params if email is present
    const urlParams = new URLSearchParams(window.location.search);
    const email = urlParams.get('email');

    if (email) {
      setForm({ ...form, email });
      setPreEmail(true);
    }
  };

  useEffect(() => {
    preloadEmail();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    setError('');
  }, [form]);

  const handleSubmit = async () => {
    setSubmitting(true);

    // Basic validation
    if (!form.firstName || !form.lastName || !form.email) {
      setError('Please fill in all required fields');
      setSubmitting(false);
      return;
    }

    // Validate email format
    if (!/\S+@\S+\.\S+/.test(form.email)) {
      setError('Please enter a valid email address');
      setSubmitting(false);
      return;
    }

    try {
      const response = await authService.register({
        email: form.email,
        first_name: form.firstName,
        last_name: form.lastName,
      });

      // Extract user_id and token from verification link
      const { userId, token } = parseVerificationLink(response.verification_link);

      if (!userId || !token) {
        throw new Error('Invalid verification link received');
      }

      // Store registration data in sessionStorage for the verification flow
      sessionStorage.setItem('registrationData', JSON.stringify({
        userId,
        token,
        email: form.email,
        firstName: form.firstName,
        lastName: form.lastName,
        title: form.title,
      }));

      setSubmitting(false);

      // Redirect to email verification page
      window.location.href = `/verify-email?token=${token}&user_id=${userId}`;
    } catch (err) {
      console.error('Registration error:', err);
      const apiError = err as ApiError;
      setError(apiError.message || 'Failed to send verification email. Please try again.');
      setSubmitting(false);
    }
  };

  return (
    <div className="from-primary/20 to-secondary/20 dark:from-primary/10 dark:to-secondary/10 flex min-h-screen items-center justify-center bg-gradient-to-br dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md border shadow-xl">
        <Page1
          form={form}
          setForm={setForm}
          onContinue={handleSubmit}
          error={error}
          submitting={submitting}
          preEmail={preEmail}
        />
      </Card>
    </div>
  );
}
