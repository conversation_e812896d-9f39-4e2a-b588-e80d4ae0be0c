'use client';

import { useState, useEffect } from 'react';
import { AUTHSTORE } from '@/lib/auth-storage';

export default function TrustedDomainsDemo() {
  const [trustedPatterns, setTrustedPatterns] = useState<string[]>([]);
  const [testOrigin, setTestOrigin] = useState('https://ng-support.com');
  const [testResult, setTestResult] = useState<boolean | null>(null);
  const [newPattern, setNewPattern] = useState('');

  useEffect(() => {
    // Get current trusted patterns
    setTrustedPatterns(AUTHSTORE.getTrustedPatterns());
  }, []);

  const testOrigin_handler = () => {
    const result = AUTHSTORE.isTrustedOrigin(testOrigin);
    setTestResult(result);
  };

  const addPattern = () => {
    if (newPattern.trim()) {
      // Note: This would require updating the AUTHSTORE to support dynamic patterns
      // For now, this is just a demo of how it would work
      console.log(`Would add pattern: ${newPattern}`);
      setNewPattern('');
    }
  };

  const testCases = [
    { origin: 'https://ng-support-fe-dev.dev1.ngnair.com', expected: true, description: 'Should match ng-*-fe-*.dev*.ngnair.com pattern' },
    { origin: 'https://ng-ob.ng-dev.ngnair.com', expected: true, description: 'Should match ng-*.ng-*.ngnair.com pattern' },
    { origin: 'https://ng-admin-fe-prod.dev2.ngnair.com', expected: true, description: 'Should match ng-*-fe-*.dev*.ngnair.com pattern' },
    { origin: 'https://ng-dashboard.ng-prod.ngnair.com', expected: true, description: 'Should match ng-*.ng-*.ngnair.com pattern' },
    { origin: 'https://ngnair.com', expected: true, description: 'Should match exact ngnair.com' },
    { origin: 'https://app.ngnair.com', expected: true, description: 'Should match *.ngnair.com pattern' },
    { origin: 'https://api.ngnair.com', expected: true, description: 'Should match *.ngnair.com pattern' },
    { origin: 'http://localhost:3000', expected: true, description: 'Should match localhost' },
    { origin: 'https://evil.com', expected: false, description: 'Should NOT match - untrusted domain' },
    { origin: 'https://ng-evil.org', expected: false, description: 'Should NOT match - wrong TLD' },
    { origin: 'https://fake-ng-support.com', expected: false, description: 'Should NOT match - not ngnair.com domain' },
    { origin: 'https://ng-support.com', expected: false, description: 'Should NOT match - not ngnair.com domain' },
  ];

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Trusted Domains Configuration</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Current Configuration */}
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Current Trusted Patterns</h2>
            <ul className="space-y-2">
              {trustedPatterns.map((pattern, index) => (
                <li key={index} className="bg-white p-3 rounded border">
                  <code className="text-blue-600">{pattern}</code>
                  <div className="text-sm text-gray-600 mt-1">
                    {pattern.includes('*') ? 'Wildcard pattern' : 'Exact match'}
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {/* Manual Test */}
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Test Origin</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Origin to test:</label>
                <input
                  type="text"
                  value={testOrigin}
                  onChange={(e) => setTestOrigin(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="https://example.com"
                />
              </div>
              <button
                onClick={testOrigin_handler}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Test Origin
              </button>
              {testResult !== null && (
                <div className={`p-3 rounded ${testResult ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  <strong>Result:</strong> {testResult ? 'TRUSTED' : 'NOT TRUSTED'}
                </div>
              )}
            </div>
          </div>

          {/* Add New Pattern */}
          <div className="bg-yellow-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Add New Pattern (Demo)</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">New pattern:</label>
                <input
                  type="text"
                  value={newPattern}
                  onChange={(e) => setNewPattern(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="example-*.com"
                />
              </div>
              <button
                onClick={addPattern}
                className="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700"
              >
                Add Pattern (Demo)
              </button>
              <p className="text-sm text-gray-600">
                Note: In production, patterns are configured via environment variables.
              </p>
            </div>
          </div>
        </div>

        {/* Test Cases */}
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Automatic Test Cases</h2>
            <div className="space-y-3">
              {testCases.map((testCase, index) => {
                const result = AUTHSTORE.isTrustedOrigin(testCase.origin);
                const isCorrect = result === testCase.expected;
                
                return (
                  <div key={index} className={`p-3 rounded border ${isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                    <div className="flex items-center justify-between">
                      <code className="text-sm">{testCase.origin}</code>
                      <span className={`px-2 py-1 rounded text-xs ${result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {result ? 'TRUSTED' : 'BLOCKED'}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {testCase.description}
                    </div>
                    {!isCorrect && (
                      <div className="text-sm text-red-600 mt-1">
                        ⚠️ Expected: {testCase.expected ? 'TRUSTED' : 'BLOCKED'}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Pattern Examples */}
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Pattern Examples</h3>
            <div className="space-y-3 text-sm">
              <div>
                <code className="bg-white p-1 rounded">ng-*-fe-*.dev*.ngnair.com</code>
                <div className="text-gray-600">Matches: ng-support-fe-dev.dev1.ngnair.com, ng-admin-fe-prod.dev2.ngnair.com</div>
              </div>
              <div>
                <code className="bg-white p-1 rounded">ng-*.ng-*.ngnair.com</code>
                <div className="text-gray-600">Matches: ng-ob.ng-dev.ngnair.com, ng-dashboard.ng-prod.ngnair.com</div>
              </div>
              <div>
                <code className="bg-white p-1 rounded">*.ngnair.com</code>
                <div className="text-gray-600">Matches: app.ngnair.com, api.ngnair.com, any.subdomain.ngnair.com</div>
              </div>
              <div>
                <code className="bg-white p-1 rounded">ngnair.com</code>
                <div className="text-gray-600">Matches: exactly ngnair.com only</div>
              </div>
              <div>
                <code className="bg-white p-1 rounded">localhost</code>
                <div className="text-gray-600">Matches: localhost (any port)</div>
              </div>
            </div>
          </div>

          {/* Environment Configuration */}
          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Environment Configuration</h3>
            <div className="text-sm space-y-2">
              <p>Add this to your <code>.env.local</code> file:</p>
              <div className="bg-white p-3 rounded border font-mono text-xs">
                NEXT_PUBLIC_TRUSTED_DOMAINS=ng-*-fe-*.dev*.ngnair.com,ng-*.ng-*.ngnair.com,*.ngnair.com,ngnair.com,localhost
              </div>
              <p className="text-gray-600">
                Comma-separated list of domain patterns. Supports wildcards (*).
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
