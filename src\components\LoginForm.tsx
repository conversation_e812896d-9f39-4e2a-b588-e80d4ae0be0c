// src/components/LoginForm.tsx
'use client';

import { useState } from 'react';
import { gql, useMutation } from '@apollo/client';
import { Button } from './ui/button';
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Checkbox } from './ui/checkbox';
import { LockIcon, MailIcon } from 'lucide-react';

const LOGIN_MUTATION = gql`
  mutation Login($email: String!, $password: String!) {
    login(email: $email, password: $password) {
      token
      mfaRequired
    }
  }
`;

interface LoginFormProps {
  onSuccess?: (mfaRequired: boolean) => void;
  showRegisterLink?: boolean;
  className?: string;
}

export default function LoginForm({ onSuccess, showRegisterLink = false, className = "" }: LoginFormProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  const [login, { loading }] = useMutation(LOGIN_MUTATION, {
    onCompleted: (data) => {
      if (!data?.login) {
        setError('Invalid response from server.');
        return;
      }
      if (onSuccess) {
        onSuccess(data.login.mfaRequired);
      } else {
        // Default behavior: redirect to dashboard
        const searchParams = new URLSearchParams(window.location.search);
        const redirect = searchParams.get('redirect');
        if (redirect) {
          window.location.href = redirect;
        } else {
          window.location.href = '/dashboard';
        }
      }
    },
    onError: (err) => {
      setError(err?.message || 'An unexpected error occurred.');
    },
  });

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!email || !password) {
      setError('Please enter both email and password.');
      return;
    }
    setError('');
    login({ variables: { email, password } });
  };

  const redirectToRegister = () => {
    const queryParams = new URLSearchParams(window.location.search);
    const registerUrl = new URL('/register', window.location.origin);
    queryParams.forEach((value, key) => {
      registerUrl.searchParams.append(key, value);
    });
    window.location.href = registerUrl.toString();
  };

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <Card className="w-full max-w-md border shadow-xl">
        <CardHeader className="space-y-1">
          <CardTitle className="text-center text-2xl font-bold">
            <img src="/images/logo.png" className="mx-auto h-16" alt="NGnair Logo" />
          </CardTitle>
          <div className="text-left">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome back
            </h2>
            {showRegisterLink && (
              <p className="text-sm text-gray-600">
                Don&apos;t have an account?{' '}
                <button
                  type="button"
                  onClick={redirectToRegister}
                  className="text-blue-600 hover:text-blue-500 h-auto w-fit p-0 font-normal"
                >
                  Sign up
                </button>
              </p>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <div className="relative">
              <MailIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                placeholder="<EMAIL>"
                type="email"
                className="pl-10"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <LockIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="password"
                type="password"
                className="pl-10"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember-me"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <Label htmlFor="remember-me" className="text-sm">
                Remember me.
              </Label>
            </div>
            <Button
              variant="link"
              className="text-sm text-blue-600 hover:text-blue-500 p-0 h-auto"
              onClick={() => window.location.href = '/forgot-password'}
            >
              Forgot password?
            </Button>
          </div>

          <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white" onClick={handleSubmit} disabled={loading}>
            {loading ? 'Signing in...' : 'Sign In'}
          </Button>
          {error && (
            <p className="-pt-2 mx-auto text-center text-xs text-red-600">{error}</p>
          )}
        </CardContent>

        {/* Demo credentials - Only show in development */}
        {process.env.NEXT_PUBLIC_APP_ENV === 'development' && (
          <CardFooter className="flex flex-col space-y-2">
            <div className="text-center text-sm text-muted-foreground">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> Walking&Forward37</p>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}