/* eslint-disable @next/next/no-img-element */
import { useState } from 'react';
import { Button } from './ui/button';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardFooter,
  CardTitle,
  CardDescription,
} from './ui/card';
import { Input } from './ui/input';

export const MFA = (props: {
  onVerify: (code: string) => void;
  onResend: () => void;
  onBack: () => void;
  error?: string;
  submitting?: boolean;
  pendingToResend?: number;
}) => {
  const [code, setCode] = useState(['', '', '', '', '', '']);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      if (value !== '' && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && code[index] === '' && index > 0) {
      const prevInput = document.getElementById(`code-${index - 1}`);
      prevInput?.focus();
    }
  };

  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/images/logo.png" className="mx-auto h-16" alt="NGnair Logo" />
        </CardTitle>
        <div className="text-left">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Enter verification code
          </h2>
          <p className="text-sm text-gray-600">
            Please enter the 6-digit code sent to your device.
          </p>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="mx-auto flex max-w-xs justify-between gap-2">
          {code.map((digit, index) => (
            <Input
              key={index}
              id={`code-${index}`}
              type="text"
              inputMode="numeric"
              pattern="\d*"
              maxLength={1}
              className="h-12 w-12 text-center text-2xl font-bold"
              value={digit}
              onChange={(e) => handleCodeChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              disabled={props.submitting}
            />
          ))}
        </div>
        <Button
          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          onClick={() => props.onVerify(code.join(''))}
          disabled={props.submitting || code.join('').length !== 6}
        >
          {props.submitting ? 'Verifying...' : 'Verify'}
        </Button>
        {props.error && <p className="text-center text-xs text-red-600">{props.error}</p>}
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        {!props.pendingToResend ? (
          <Button 
            variant="link" 
            onClick={props.onResend} 
            className="text-sm text-blue-600 hover:text-blue-500"
            disabled={props.submitting}
          >
            Resend code
          </Button>
        ) : (
          <p className="text-center text-sm text-gray-600">
            Resend code in {props.pendingToResend} seconds
          </p>
        )}
        <Button 
          variant="link" 
          onClick={props.onBack} 
          className="text-sm text-blue-600 hover:text-blue-500"
          disabled={props.submitting}
        >
          Go Back
        </Button>
      </CardFooter>
    </>
  );
};
