import { authService } from '../api/auth-service'
import { JWTUtils } from '../jwt-utils'
import { AUTHSTORE } from '../auth-storage'
import { createMockJWTToken, createValidJWTToken, createExpiredJWTToken } from '../test-utils/jwt-test-utils'

// Mock dependencies
jest.mock('../auth-storage')

describe('JWT Integration Tests', () => {
  const mockAuthStore = AUTHSTORE as jest.Mocked<typeof AUTHSTORE>
  const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>

  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockClear()
  })

  describe('Complete JWT Flow', () => {
    it('should handle complete login and token validation flow', async () => {
      // Step 1: Mock successful login response
      const mockLoginResponse = {
        access_token: 'mock-jwt-token',
        device_id: 'device-123',
        success: 'True',
      }

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockLoginResponse),
      }
      mockFetch.mockResolvedValue(mockResponse as any)

      // Step 2: Perform login
      const loginResult = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
      })

      expect(loginResult).toEqual(mockLoginResponse)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/login?device_id'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-Device-ID': 'device-uuid-123',
          }),
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
          }),
        })
      )

      // Step 3: Simulate token storage
      const mockJWTToken = createValidJWTToken({
        accounts: [
          {
            id: 'acc1',
            scopes: ['account:view', 'account:manage'],
          },
        ],
        partners: [
          {
            id: 'partner1',
            scopes: ['partner:view'],
          },
        ],
        selected_account_id: 'acc1',
        selected_partner_id: null,
      })

      mockAuthStore.get.mockReturnValue(mockJWTToken)

      // Step 4: Validate token using JWTUtils
      const isValid = JWTUtils.isTokenValid()
      expect(isValid).toBe(true)

      const isExpired = JWTUtils.isTokenExpired()
      expect(isExpired).toBe(false)

      // Step 5: Extract user context
      const userContext = JWTUtils.getUserContext()
      expect(userContext).toEqual({
        userId: 'user123',
        accounts: [
          {
            id: 'acc1',
            scopes: ['account:view', 'account:manage'],
          },
        ],
        partners: [
          {
            id: 'partner1',
            scopes: ['partner:view'],
          },
        ],
        selectedAccountId: 'acc1',
        selectedPartnerId: null,
      })

      // Step 6: Test scope checking
      const hasAccountView = JWTUtils.hasAccountScope('acc1', 'account:view')
      expect(hasAccountView).toBe(true)

      const hasAccountManage = JWTUtils.hasAccountScope('acc1', 'account:manage')
      expect(hasAccountManage).toBe(true)

      const hasPartnerView = JWTUtils.hasPartnerScope('partner1', 'partner:view')
      expect(hasPartnerView).toBe(true)

      const hasUnauthorizedScope = JWTUtils.hasAccountScope('acc1', 'account:delete')
      expect(hasUnauthorizedScope).toBe(false)
    })

    it('should handle expired token scenario', () => {
      // Create expired token
      const expiredToken = createExpiredJWTToken()

      mockAuthStore.get.mockReturnValue(expiredToken)

      // Test token validation
      const isValid = JWTUtils.isTokenValid()
      expect(isValid).toBe(false)

      const isExpired = JWTUtils.isTokenExpired()
      expect(isExpired).toBe(true)

      // Auth service should also detect expiration
      const authServiceValid = authService.validateToken()
      expect(authServiceValid).toBe(false)
    })

    it('should handle malformed token scenario', () => {
      mockAuthStore.get.mockReturnValue('invalid-token-format')

      // Test token validation
      const isValid = JWTUtils.isTokenValid()
      expect(isValid).toBe(false)

      const userContext = JWTUtils.getUserContext()
      expect(userContext).toEqual({
        userId: null,
        accounts: [],
        partners: [],
        selectedAccountId: null,
        selectedPartnerId: null,
      })

      // Auth service should also detect invalid format
      const authServiceValid = authService.validateToken()
      expect(authServiceValid).toBe(false)
    })

    it('should handle JWKS fetching for token verification', async () => {
      const mockJWKS = {
        keys: [
          {
            kty: 'RSA',
            use: 'sig',
            kid: 'key1',
            n: 'mock-n-value',
            e: 'AQAB',
          },
        ],
      }

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockJWKS),
      }
      mockFetch.mockResolvedValue(mockResponse as any)

      const jwks = await JWTUtils.getJWKS()

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/jwks'),
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      )
      expect(jwks).toEqual(mockJWKS)
    })

    it('should handle API requests with JWT token', async () => {
      const mockToken = 'mock-jwt-token'
      mockAuthStore.get.mockReturnValue(mockToken)

      const mockApiResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ data: 'success' }),
      }
      mockFetch.mockResolvedValue(mockApiResponse as any)

      // Make an API request that should include the JWT token
      await authService.verifyMfa({
        user_id: 'user123',
        mfa_code: '123456',
        mfa_type: 'sms',
      })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/mfa_verify'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockToken}`,
            'Content-Type': 'application/json',
          }),
        })
      )
    })
  })

  describe('Token Scope Validation', () => {
    it('should correctly validate account scopes', () => {
      const tokenWithScopes = createMockJWTToken({
        sub: 'user123',
        exp: Math.floor(Date.now() / 1000) + 3600,
        accounts: [
          {
            id: 'acc1',
            scopes: ['account:view', 'transaction:create'],
          },
          {
            id: 'acc2',
            scopes: ['account:view'],
          },
        ],
        selected_account_id: 'acc1',
      })

      // Test with specific token
      const hasScope1 = JWTUtils.hasAccountScope('acc1', 'account:view', tokenWithScopes)
      expect(hasScope1).toBe(true)

      const hasScope2 = JWTUtils.hasAccountScope('acc1', 'transaction:create', tokenWithScopes)
      expect(hasScope2).toBe(true)

      const hasScope3 = JWTUtils.hasAccountScope('acc2', 'account:view', tokenWithScopes)
      expect(hasScope3).toBe(true)

      const hasScope4 = JWTUtils.hasAccountScope('acc2', 'transaction:create', tokenWithScopes)
      expect(hasScope4).toBe(false)

      const hasScope5 = JWTUtils.hasAccountScope('acc3', 'account:view', tokenWithScopes)
      expect(hasScope5).toBe(false)
    })

    it('should correctly validate partner scopes', () => {
      const tokenWithScopes = createMockJWTToken({
        sub: 'user123',
        exp: Math.floor(Date.now() / 1000) + 3600,
        partners: [
          {
            id: 'partner1',
            scopes: ['partner:view', 'partner:manage_accounts'],
          },
        ],
        selected_partner_id: 'partner1',
      })

      const hasScope1 = JWTUtils.hasPartnerScope('partner1', 'partner:view', tokenWithScopes)
      expect(hasScope1).toBe(true)

      const hasScope2 = JWTUtils.hasPartnerScope('partner1', 'partner:manage_accounts', tokenWithScopes)
      expect(hasScope2).toBe(true)

      const hasScope3 = JWTUtils.hasPartnerScope('partner1', 'partner:delete', tokenWithScopes)
      expect(hasScope3).toBe(false)
    })
  })
})

// Helper function to create mock JWT tokens
function createMockJWTToken(payload: any): string {
  const header = { alg: 'HS256', typ: 'JWT' }
  const encodedHeader = btoa(JSON.stringify(header))
  const encodedPayload = btoa(JSON.stringify(payload))
  return `${encodedHeader}.${encodedPayload}.mock-signature`
}
