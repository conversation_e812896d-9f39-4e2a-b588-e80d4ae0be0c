import { JWTUtils } from '../jwt-utils'
import { authService } from '../api/auth-service'

// Mock the auth service
jest.mock('../api/auth-service', () => ({
  authService: {
    getJWKS: jest.fn(),
    decodeTokenPayload: jest.fn(),
    validateToken: jest.fn(),
    getCurrentUserContext: jest.fn(),
  },
}))

describe('JWTUtils', () => {
  const mockAuthService = authService as jest.Mocked<typeof authService>

  beforeEach(() => {
    jest.clearAllMocks()
    // Clear static cache
    ;(JWTUtils as any).jwksCache = null
    ;(JWTUtils as any).jwksCacheExpiry = 0
  })

  describe('decodePayload', () => {
    it('should decode a valid JWT token', () => {
      // Create a mock JWT token (header.payload.signature)
      const mockPayload = {
        sub: 'user123',
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        iat: Math.floor(Date.now() / 1000),
        accounts: [{ id: 'acc1', scopes: ['account:view'] }],
        partners: [{ id: 'partner1', scopes: ['partner:view'] }],
        selected_account_id: 'acc1',
        selected_partner_id: null,
      }

      const encodedPayload = btoa(JSON.stringify(mockPayload))
      const mockToken = `header.${encodedPayload}.signature`

      const result = JWTUtils.decodePayload(mockToken)

      expect(result).toEqual(mockPayload)
    })

    it('should return null for invalid JWT token', () => {
      const invalidToken = 'invalid.token'
      const result = JWTUtils.decodePayload(invalidToken)
      expect(result).toBeNull()
    })

    it('should return null for malformed JWT token', () => {
      const malformedToken = 'header.invalid-base64.signature'
      const result = JWTUtils.decodePayload(malformedToken)
      expect(result).toBeNull()
    })

    it('should call authService.decodeTokenPayload when no token provided', () => {
      const mockPayload = { sub: 'user123', exp: ********** }
      mockAuthService.decodeTokenPayload.mockReturnValue(mockPayload)

      const result = JWTUtils.decodePayload()

      expect(mockAuthService.decodeTokenPayload).toHaveBeenCalled()
      expect(result).toEqual(mockPayload)
    })
  })

  describe('isTokenExpired', () => {
    it('should return false for non-expired token', () => {
      const futureExp = Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
      const mockPayload = { sub: 'user123', exp: futureExp }
      const encodedPayload = btoa(JSON.stringify(mockPayload))
      const mockToken = `header.${encodedPayload}.signature`

      const result = JWTUtils.isTokenExpired(mockToken)
      expect(result).toBe(false)
    })

    it('should return true for expired token', () => {
      const pastExp = Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
      const mockPayload = { sub: 'user123', exp: pastExp }
      const encodedPayload = btoa(JSON.stringify(mockPayload))
      const mockToken = `header.${encodedPayload}.signature`

      const result = JWTUtils.isTokenExpired(mockToken)
      expect(result).toBe(true)
    })

    it('should return true for token without exp claim', () => {
      const mockPayload = { sub: 'user123' } // No exp claim
      const encodedPayload = btoa(JSON.stringify(mockPayload))
      const mockToken = `header.${encodedPayload}.signature`

      const result = JWTUtils.isTokenExpired(mockToken)
      expect(result).toBe(true)
    })

    it('should return true for invalid token', () => {
      const result = JWTUtils.isTokenExpired('invalid-token')
      expect(result).toBe(true)
    })
  })

  describe('isTokenValid', () => {
    it('should return true for valid non-expired token', () => {
      const futureExp = Math.floor(Date.now() / 1000) + 3600
      const mockPayload = { sub: 'user123', exp: futureExp }
      const encodedPayload = btoa(JSON.stringify(mockPayload))
      const mockToken = `header.${encodedPayload}.signature`

      const result = JWTUtils.isTokenValid(mockToken)
      expect(result).toBe(true)
    })

    it('should return false for expired token', () => {
      const pastExp = Math.floor(Date.now() / 1000) - 3600
      const mockPayload = { sub: 'user123', exp: pastExp }
      const encodedPayload = btoa(JSON.stringify(mockPayload))
      const mockToken = `header.${encodedPayload}.signature`

      const result = JWTUtils.isTokenValid(mockToken)
      expect(result).toBe(false)
    })

    it('should call authService.validateToken when no token provided', () => {
      mockAuthService.validateToken.mockReturnValue(true)

      const result = JWTUtils.isTokenValid()

      expect(mockAuthService.validateToken).toHaveBeenCalled()
      expect(result).toBe(true)
    })
  })

  describe('getUserContext', () => {
    it('should extract user context from token', () => {
      const mockPayload = {
        sub: 'user123',
        exp: Math.floor(Date.now() / 1000) + 3600,
        accounts: [{ id: 'acc1', scopes: ['account:view'] }],
        partners: [{ id: 'partner1', scopes: ['partner:view'] }],
        selected_account_id: 'acc1',
        selected_partner_id: 'partner1',
      }
      const encodedPayload = btoa(JSON.stringify(mockPayload))
      const mockToken = `header.${encodedPayload}.signature`

      const result = JWTUtils.getUserContext(mockToken)

      expect(result).toEqual({
        userId: 'user123',
        accounts: [{ id: 'acc1', scopes: ['account:view'] }],
        partners: [{ id: 'partner1', scopes: ['partner:view'] }],
        selectedAccountId: 'acc1',
        selectedPartnerId: 'partner1',
      })
    })

    it('should return default context for invalid token', () => {
      const result = JWTUtils.getUserContext('invalid-token')

      expect(result).toEqual({
        userId: null,
        accounts: [],
        partners: [],
        selectedAccountId: null,
        selectedPartnerId: null,
      })
    })

    it('should call authService.getCurrentUserContext when no token provided', () => {
      const mockContext = {
        userId: 'user123',
        accounts: [],
        partners: [],
        selectedAccountId: null,
        selectedPartnerId: null,
      }
      mockAuthService.getCurrentUserContext.mockReturnValue(mockContext)

      const result = JWTUtils.getUserContext()

      expect(mockAuthService.getCurrentUserContext).toHaveBeenCalled()
      expect(result).toEqual(mockContext)
    })
  })

  describe('getJWKS', () => {
    it('should fetch JWKS from auth service', async () => {
      const mockJWKS = {
        keys: [
          {
            kty: 'RSA',
            use: 'sig',
            kid: 'key1',
            n: 'mock-n-value',
            e: 'AQAB',
          },
        ],
      }
      mockAuthService.getJWKS.mockResolvedValue(mockJWKS)

      const result = await JWTUtils.getJWKS()

      expect(mockAuthService.getJWKS).toHaveBeenCalled()
      expect(result).toEqual(mockJWKS)
    })

    it('should use cached JWKS if still valid', async () => {
      const mockJWKS = {
        keys: [
          {
            kty: 'RSA',
            use: 'sig',
            kid: 'key1',
            n: 'mock-n-value',
            e: 'AQAB',
          },
        ],
      }

      // Set up cache
      ;(JWTUtils as any).jwksCache = mockJWKS
      ;(JWTUtils as any).jwksCacheExpiry = Date.now() + 3600000 // 1 hour from now

      const result = await JWTUtils.getJWKS()

      expect(mockAuthService.getJWKS).not.toHaveBeenCalled()
      expect(result).toEqual(mockJWKS)
    })

    it('should refresh cache if expired', async () => {
      const oldJWKS = { keys: [{ kty: 'RSA', use: 'sig', kid: 'old-key', n: 'old-n', e: 'AQAB' }] }
      const newJWKS = { keys: [{ kty: 'RSA', use: 'sig', kid: 'new-key', n: 'new-n', e: 'AQAB' }] }

      // Set up expired cache
      ;(JWTUtils as any).jwksCache = oldJWKS
      ;(JWTUtils as any).jwksCacheExpiry = Date.now() - 1000 // 1 second ago

      mockAuthService.getJWKS.mockResolvedValue(newJWKS)

      const result = await JWTUtils.getJWKS()

      expect(mockAuthService.getJWKS).toHaveBeenCalled()
      expect(result).toEqual(newJWKS)
    })

    it('should throw error if JWKS fetch fails', async () => {
      const error = new Error('JWKS fetch failed')
      mockAuthService.getJWKS.mockRejectedValue(error)

      await expect(JWTUtils.getJWKS()).rejects.toThrow('JWKS fetch failed')
    })
  })
})
