import { authService } from '../auth-service'
import { AUTHSTORE } from '../../auth-storage'

// Mock AUTHSTORE
jest.mock('../../auth-storage', () => ({
  AUTHSTORE: {
    get: jest.fn(),
    set: jest.fn(),
    clear: jest.fn(),
  },
}))

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>

describe('AuthService', () => {
  const mockAuthStore = AUTHSTORE as jest.Mocked<typeof AUTHSTORE>

  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockClear()
  })

  describe('makeRequest', () => {
    it('should include Authorization header when token is available', async () => {
      const mockToken = 'mock-jwt-token'
      mockAuthStore.get.mockReturnValue(mockToken)

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ success: true }),
      }
      mockFetch.mockResolvedValue(mockResponse as any)

      await authService.login({ email: '<EMAIL>', password: 'password' })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/login?device_id'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': `Bearer ${mockToken}`,
            'Content-Type': 'application/json',
            'X-Device-ID': 'device-uuid-123',
          }),
        })
      )
    })

    it('should not include Authorization header when no token is available', async () => {
      mockAuthStore.get.mockReturnValue('')

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ success: true }),
      }
      mockFetch.mockResolvedValue(mockResponse as any)

      await authService.login({ email: '<EMAIL>', password: 'password' })

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/login?device_id'),
        expect.objectContaining({
          headers: expect.not.objectContaining({
            'Authorization': expect.any(String),
          }),
        })
      )
    })

    it('should handle API errors properly', async () => {
      mockAuthStore.get.mockReturnValue('')

      const mockResponse = {
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: jest.fn().mockResolvedValue({ message: 'Invalid credentials' }),
      }
      mockFetch.mockResolvedValue(mockResponse as any)

      await expect(
        authService.login({ email: '<EMAIL>', password: 'wrong-password' })
      ).rejects.toEqual({
        message: 'Invalid credentials',
        status: 401,
      })
    })

    it('should handle network errors', async () => {
      mockAuthStore.get.mockReturnValue('')
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(
        authService.login({ email: '<EMAIL>', password: 'password' })
      ).rejects.toEqual({
        message: 'Network error',
        status: 0,
      })
    })
  })

  describe('validateToken', () => {
    it('should return false when no token is available', () => {
      mockAuthStore.get.mockReturnValue('')

      const result = authService.validateToken()

      expect(result).toBe(false)
    })

    it('should return false for invalid JWT structure', () => {
      mockAuthStore.get.mockReturnValue('invalid-token')

      const result = authService.validateToken()

      expect(result).toBe(false)
    })

    it('should return false for expired token', () => {
      const expiredPayload = {
        sub: 'user123',
        exp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
        iat: Math.floor(Date.now() / 1000) - 7200, // 2 hours ago
      }
      const encodedPayload = btoa(JSON.stringify(expiredPayload))
      const expiredToken = `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.${encodedPayload}.signature`

      mockAuthStore.get.mockReturnValue(expiredToken)

      const result = authService.validateToken()

      expect(result).toBe(false)
    })

    it('should return true for valid non-expired token', () => {
      const validPayload = {
        sub: 'user123',
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        iat: Math.floor(Date.now() / 1000),
      }
      const encodedPayload = btoa(JSON.stringify(validPayload))
      const validToken = `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.${encodedPayload}.signature`

      mockAuthStore.get.mockReturnValue(validToken)

      const result = authService.validateToken()

      expect(result).toBe(true)
    })
  })

  describe('decodeTokenPayload', () => {
    it('should decode valid JWT payload', () => {
      const mockPayload = {
        sub: 'user123',
        exp: Math.floor(Date.now() / 1000) + 3600,
        iat: Math.floor(Date.now() / 1000),
        accounts: [{ id: 'acc1', scopes: ['account:view'] }],
      }
      const encodedPayload = btoa(JSON.stringify(mockPayload))
      const mockToken = `header.${encodedPayload}.signature`

      mockAuthStore.get.mockReturnValue(mockToken)

      const result = authService.decodeTokenPayload()

      expect(result).toEqual(mockPayload)
    })

    it('should return null when no token is available', () => {
      mockAuthStore.get.mockReturnValue('')

      const result = authService.decodeTokenPayload()

      expect(result).toBeNull()
    })

    it('should return null for invalid token structure', () => {
      mockAuthStore.get.mockReturnValue('invalid.token')

      const result = authService.decodeTokenPayload()

      expect(result).toBeNull()
    })

    it('should return null for malformed base64 payload', () => {
      mockAuthStore.get.mockReturnValue('header.invalid-base64.signature')

      const result = authService.decodeTokenPayload()

      expect(result).toBeNull()
    })
  })

  describe('getJWKS', () => {
    it('should fetch JWKS successfully', async () => {
      const mockJWKS = {
        keys: [
          {
            kty: 'RSA',
            use: 'sig',
            kid: 'key1',
            n: 'mock-n-value',
            e: 'AQAB',
          },
        ],
      }

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockJWKS),
      }
      mockFetch.mockResolvedValue(mockResponse as any)

      const result = await authService.getJWKS()

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/jwks'),
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      )
      expect(result).toEqual(mockJWKS)
    })

    it('should throw error when JWKS fetch fails', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      }
      mockFetch.mockResolvedValue(mockResponse as any)

      await expect(authService.getJWKS()).rejects.toThrow('JWKS fetch failed: 500')
    })

    it('should throw error on network failure', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(authService.getJWKS()).rejects.toThrow('Network error')
    })
  })

  describe('JWT structure validation (through validateToken)', () => {
    it('should return true for valid JWT structure', () => {
      const validHeader = { alg: 'HS256', typ: 'JWT' }
      const validPayload = { sub: 'user123', exp: Math.floor(Date.now() / 1000) + 3600 }
      const encodedHeader = btoa(JSON.stringify(validHeader))
      const encodedPayload = btoa(JSON.stringify(validPayload))
      const validToken = `${encodedHeader}.${encodedPayload}.signature`

      mockAuthStore.get.mockReturnValue(validToken)

      const result = authService.validateToken()

      expect(result).toBe(true)
    })

    it('should return false for token without 3 parts', () => {
      const invalidToken = 'header.payload'

      mockAuthStore.get.mockReturnValue(invalidToken)

      const result = authService.validateToken()

      expect(result).toBe(false)
    })

    it('should return false for token with invalid base64 encoding', () => {
      const invalidToken = 'invalid-base64.invalid-base64.signature'

      mockAuthStore.get.mockReturnValue(invalidToken)

      const result = authService.validateToken()

      expect(result).toBe(false)
    })

    it('should return false for token without required fields', () => {
      const invalidHeader = { typ: 'JWT' } // Missing alg
      const invalidPayload = { sub: 'user123' } // Missing exp
      const encodedHeader = btoa(JSON.stringify(invalidHeader))
      const encodedPayload = btoa(JSON.stringify(invalidPayload))
      const invalidToken = `${encodedHeader}.${encodedPayload}.signature`

      mockAuthStore.get.mockReturnValue(invalidToken)

      const result = authService.validateToken()

      expect(result).toBe(false)
    })
  })

  describe('Token expiration validation (through validateToken)', () => {
    it('should return true for non-expired token', () => {
      const validHeader = { alg: 'HS256', typ: 'JWT' }
      const futureExp = Math.floor(Date.now() / 1000) + 3600
      const validPayload = { sub: 'user123', exp: futureExp }
      const encodedHeader = btoa(JSON.stringify(validHeader))
      const encodedPayload = btoa(JSON.stringify(validPayload))
      const validToken = `${encodedHeader}.${encodedPayload}.signature`

      mockAuthStore.get.mockReturnValue(validToken)

      const result = authService.validateToken()

      expect(result).toBe(true)
    })

    it('should return false for expired token', () => {
      const validHeader = { alg: 'HS256', typ: 'JWT' }
      const pastExp = Math.floor(Date.now() / 1000) - 3600
      const expiredPayload = { sub: 'user123', exp: pastExp }
      const encodedHeader = btoa(JSON.stringify(validHeader))
      const encodedPayload = btoa(JSON.stringify(expiredPayload))
      const expiredToken = `${encodedHeader}.${encodedPayload}.signature`

      mockAuthStore.get.mockReturnValue(expiredToken)

      const result = authService.validateToken()

      expect(result).toBe(false)
    })

    it('should return false for token without exp claim', () => {
      const validHeader = { alg: 'HS256', typ: 'JWT' }
      const payloadWithoutExp = { sub: 'user123' }
      const encodedHeader = btoa(JSON.stringify(validHeader))
      const encodedPayload = btoa(JSON.stringify(payloadWithoutExp))
      const tokenWithoutExp = `${encodedHeader}.${encodedPayload}.signature`

      mockAuthStore.get.mockReturnValue(tokenWithoutExp)

      const result = authService.validateToken()

      expect(result).toBe(false)
    })
  })
})
