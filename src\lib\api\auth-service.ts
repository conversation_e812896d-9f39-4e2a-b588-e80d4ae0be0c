// src/lib/api/auth-service.ts
import { AUTHSTORE } from '../auth-storage';

const API_BASE_URL = 'https://ng-auth-dev.dev1.ngnair.com/api/v1';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  mfa_required?: boolean;
  methods?: string[];
  message?: string;
  user_id?: string;
  mfa_token?: string;
  token?: string;
  phone_number?: string;
  // Rails backend response fields
  access_token?: string;
  device_id?: string;
  // Success response (can be boolean or string)
  success?: boolean | string;
}

export interface MfaVerifyRequest {
  user_id: string;
  otp_code: number;
  mfa_token: string;
}

export interface MfaVerifyResponse {
  success: boolean;
  token?: string;
  message?: string;
}

export interface UserInfo {
  user_id: string;
  email: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
}

export interface RegisterRequest {
  email: string;
  first_name: string;
  last_name: string;
}

export interface RegisterResponse {
  message: string;
  verification_link: string;
  // We can extract user_id and token from verification_link
}

export interface VerifyEmailRequest {
  user_id: string;
  token: string;
}

export interface VerifyEmailResponse {
  message: string;
  access_token?: string;
  // On success, should provide access_token
}

export interface SendOtpRequest {
  user_id: string;
  phone: string;
}

export interface SendOtpResponse {
  message: string;
  otp: string; // For development/testing - may not be in production
}

export interface VerifyOtpRequest {
  user_id: string;
  otp: string;
}

export interface VerifyOtpResponse {
  message: string;
}

export interface SetPasswordRequest {
  user_id: string;
  password: string;
}

export interface SetPasswordResponse {
  access_token: string;
  device_id: string;
}

export interface ApiError {
  message: string;
  status: number;
}

export interface JWTPayload {
  sub: string; // user_id
  iss: string; // issuer
  iat: number; // issued at
  exp: number; // expiration
  accounts?: Array<{
    id: string;
    role: string;
    scopes: string[];
  }>;
  partners?: Array<{
    id: string;
    role: string;
    scopes: string[];
  }>;
  selected_account_id?: string; // optional — active context
  selected_partner_id?: string | null; // optional — null if not in partner context
}

// Utility function to extract data from verification link
export function parseVerificationLink(verificationLink: string): { userId: string | null; token: string | null } {
  try {
    const urlParts = verificationLink.split('?');
    if (urlParts.length < 2) return { userId: null, token: null };

    const params = new URLSearchParams(urlParts[1]);
    return {
      userId: params.get('user_id'),
      token: params.get('token')
    };
  } catch {
    return { userId: null, token: null };
  }
}

class AuthService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers as Record<string, string>,
    };

    // Add auth token if available
    const token = AUTHSTORE.get();
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers: defaultHeaders,
        credentials: 'include', // Equivalent to withCredentials: true in axios
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw {
          message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
        } as ApiError;
      }

      return await response.json();
    } catch (error) {
      if (error && typeof error === 'object' && 'status' in error) {
        throw error;
      }
      
      throw {
        message: error instanceof Error ? error.message : 'Network error occurred',
        status: 0,
      } as ApiError;
    }
  }

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    // Debug logging (only in development)
    if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
      console.log('Frontend login request:');
      console.log('URL:', `${API_BASE_URL}/login?device_id`);
      console.log('Credentials:', credentials);
      console.log('Request body:', JSON.stringify(credentials));
    }

    // Add device_id query parameter and X-Device-ID header to match the working Postman request
    const response = await this.makeRequest<LoginResponse>('/login?device_id', {
      method: 'POST',
      body: JSON.stringify(credentials),
      headers: {
        'X-Device-ID': 'device-uuid-123',
      },
    });

    if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
      console.log('Added X-Device-ID header: device-uuid-123');
    }

    // The API might return user_id in the response for MFA flow
    // We need to extract it from the response or error handling
    return response;
  }

  async verifyMfa(mfaData: MfaVerifyRequest): Promise<MfaVerifyResponse> {
    return this.makeRequest<MfaVerifyResponse>('/mfa_verify', {
      method: 'POST',
      body: JSON.stringify(mfaData),
    });
  }

  // Optional: Add method to get user info if the API supports it
  async getUserInfo(userId: string): Promise<UserInfo> {
    return this.makeRequest<UserInfo>(`/user/${userId}`, {
      method: 'GET',
    });
  }

  // Method to validate current token using local JWT validation (no API calls)
  validateToken(): boolean {
    try {
      const token = AUTHSTORE.get();
      if (!token) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('No token found');
        }
        return false;
      }

      // Basic JWT structure validation
      const isValidJWT = this.isValidJWTStructure(token);

      if (!isValidJWT) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Token has invalid JWT structure');
        }
        return false;
      }

      // Check if token is expired
      const isExpired = this.isTokenExpired(token);

      if (isExpired) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Token is expired');
        }
        return false;
      }

      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Token validation successful (local validation only)');
      }

      return true;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Token validation failed:', error);
      }
      return false;
    }
  }

  // Helper method to check JWT structure
  private isValidJWTStructure(token: string): boolean {
    try {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Validating JWT structure for token:', token.substring(0, 50) + '...');
        console.log('Token length:', token.length);
      }

      const parts = token.split('.');
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('JWT parts count:', parts.length);
        console.log('Part lengths:', parts.map(p => p.length));
      }

      if (parts.length !== 3) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Invalid JWT: Expected 3 parts, got', parts.length);
        }
        return false;
      }

      // Try to decode the header and payload
      let header, payload;

      try {
        header = JSON.parse(atob(parts[0]));
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('JWT header:', header);
        }
      } catch (e) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Failed to decode JWT header:', e.message);
          console.log('Header part:', parts[0]);
        }
        return false;
      }

      try {
        payload = JSON.parse(atob(parts[1]));
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('JWT payload:', payload);
        }
      } catch (e) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Failed to decode JWT payload:', e.message);
          console.log('Payload part:', parts[1]);
        }
        return false;
      }

      // Basic checks
      const isValid = header && payload && header.alg && payload.exp;
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('JWT validation result:', isValid);
        console.log('Header has alg:', !!header.alg);
        console.log('Payload has exp:', !!payload.exp);
      }

      return isValid;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('JWT structure validation error:', error);
      }
      return false;
    }
  }

  // Helper method to check if token is expired
  private isTokenExpired(token: string): boolean {
    try {
      const parts = token.split('.');
      const payload = JSON.parse(atob(parts[1]));

      if (!payload.exp) {
        if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
          console.log('Token has no expiration claim');
        }
        return true; // No expiration claim means invalid
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const isExpired = payload.exp < currentTime;

      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Token expiration check:');
        console.log('Current time:', currentTime, new Date(currentTime * 1000).toLocaleString());
        console.log('Token expires:', payload.exp, new Date(payload.exp * 1000).toLocaleString());
        console.log('Is expired:', isExpired);
      }

      return isExpired;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Token expiration check error:', error);
      }
      return true; // If we can't decode, consider it expired
    }
  }

  // Method to get JWKS for proper JWT validation (for future implementation)
  async getJWKS(): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/jwks`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`JWKS fetch failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('JWKS fetch failed:', error);
      }
      throw error;
    }
  }

  // Method to decode JWT payload without verification (for client-side use)
  decodeTokenPayload(): JWTPayload | null {
    try {
      const token = AUTHSTORE.get();
      if (!token) {
        return null;
      }

      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      const payload = JSON.parse(atob(parts[1]));
      return payload as JWTPayload;
    } catch (error) {
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Failed to decode token payload:', error);
      }
      return null;
    }
  }

  // Method to get current user context
  getCurrentUserContext(): {
    userId: string | null;
    accounts: Array<{ id: string; role: string; scopes: string[] }>;
    partners: Array<{ id: string; role: string; scopes: string[] }>;
    selectedAccountId: string | null;
    selectedPartnerId: string | null;
  } {
    const payload = this.decodeTokenPayload();

    if (!payload) {
      return {
        userId: null,
        accounts: [],
        partners: [],
        selectedAccountId: null,
        selectedPartnerId: null,
      };
    }

    return {
      userId: payload.sub || null,
      accounts: payload.accounts || [],
      partners: payload.partners || [],
      selectedAccountId: payload.selected_account_id || null,
      selectedPartnerId: payload.selected_partner_id || null,
    };
  }

  // Method to check if user has specific scope for an account
  hasAccountScope(accountId: string, scope: string): boolean {
    const context = this.getCurrentUserContext();
    const account = context.accounts.find(acc => acc.id === accountId);
    return account ? account.scopes.includes(scope) : false;
  }

  // Method to check if user has specific scope for a partner
  hasPartnerScope(partnerId: string, scope: string): boolean {
    const context = this.getCurrentUserContext();
    const partner = context.partners.find(p => p.id === partnerId);
    return partner ? partner.scopes.includes(scope) : false;
  }

  // Registration endpoints
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    return this.makeRequest<RegisterResponse>('/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async verifyEmail(data: VerifyEmailRequest): Promise<VerifyEmailResponse> {
    return this.makeRequest<VerifyEmailResponse>('/verify_email', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async sendOtp(data: SendOtpRequest): Promise<SendOtpResponse> {
    return this.makeRequest<SendOtpResponse>('/send_otp', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async verifyOtp(data: VerifyOtpRequest): Promise<VerifyOtpResponse> {
    return this.makeRequest<VerifyOtpResponse>('/verify_otp', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // This requires access token and stores the final access token
  async setPassword(data: SetPasswordRequest): Promise<SetPasswordResponse> {
    const response = await this.makeRequest<SetPasswordResponse>('/set_password', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    // Store the access token after successful password setup
    if (response.access_token) {
      AUTHSTORE.set(response.access_token);
    }

    // Store device_id for session management (Rails backend sets httpOnly cookies)
    if (response.device_id && typeof window !== 'undefined') {
      sessionStorage.setItem('device_id', response.device_id);
      if (process.env.NEXT_PUBLIC_APP_ENV === 'development') {
        console.log('Registration complete - Device ID:', response.device_id);
        console.log('Access token stored, Rails backend should have set httpOnly cookies');
      }
    }

    return response;
  }
}

export const authService = new AuthService();
