/**
 * JWT Testing Utilities
 * 
 * This file contains helper functions for testing JWT functionality
 */

export interface MockJWTPayload {
  sub?: string
  exp?: number
  iat?: number
  accounts?: Array<{
    id: string
    scopes: string[]
  }>
  partners?: Array<{
    id: string
    scopes: string[]
  }>
  selected_account_id?: string | null
  selected_partner_id?: string | null
  email?: string
  device_id?: string
  roles?: string[]
  permissions?: string[]
}

/**
 * Creates a mock JWT token with the given payload
 */
export function createMockJWTToken(payload: MockJWTPayload): string {
  const header = {
    alg: 'HS256',
    typ: 'JWT',
  }

  const encodedHeader = btoa(JSON.stringify(header))
  const encodedPayload = btoa(JSON.stringify(payload))
  
  return `${encodedHeader}.${encodedPayload}.mock-signature`
}

/**
 * Creates a valid (non-expired) JWT token with default values
 */
export function createValidJWTToken(overrides: Partial<MockJWTPayload> = {}): string {
  const defaultPayload: MockJWTPayload = {
    sub: 'user123',
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
    iat: Math.floor(Date.now() / 1000),
    email: '<EMAIL>',
    device_id: 'device-123',
    accounts: [
      {
        id: 'acc1',
        scopes: ['account:view', 'account:manage'],
      },
    ],
    partners: [
      {
        id: 'partner1',
        scopes: ['partner:view'],
      },
    ],
    selected_account_id: 'acc1',
    selected_partner_id: null,
    roles: ['user'],
    permissions: ['read', 'write'],
  }

  return createMockJWTToken({ ...defaultPayload, ...overrides })
}

/**
 * Creates an expired JWT token
 */
export function createExpiredJWTToken(overrides: Partial<MockJWTPayload> = {}): string {
  const expiredPayload: MockJWTPayload = {
    sub: 'user123',
    exp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
    iat: Math.floor(Date.now() / 1000) - 7200, // 2 hours ago
    email: '<EMAIL>',
    device_id: 'device-123',
    ...overrides,
  }

  return createMockJWTToken(expiredPayload)
}

/**
 * Mock JWKS response for testing
 */
export function createMockJWKS() {
  return {
    keys: [
      {
        kty: 'RSA',
        use: 'sig',
        kid: 'key1',
        n: 'mock-n-value-for-testing',
        e: 'AQAB',
      },
    ],
  }
}

/**
 * Mock API responses for testing
 */
export const mockApiResponses = {
  loginSuccess: {
    access_token: 'mock-jwt-token',
    device_id: 'device-123',
    success: 'True',
  },
  
  loginFailure: {
    message: 'Invalid credentials',
    error: 'unauthorized',
  },
  
  mfaRequired: {
    mfa_required: true,
    user_id: 'user123',
    mfa_types: ['sms', 'authenticator'],
  },
}

/**
 * Helper to mock fetch responses
 */
export function mockFetchResponse(data: any, ok: boolean = true, status: number = 200) {
  return {
    ok,
    status,
    statusText: ok ? 'OK' : 'Error',
    json: jest.fn().mockResolvedValue(data),
  }
}
